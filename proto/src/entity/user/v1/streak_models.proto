syntax = "proto3";

package entity.user.v1;

option go_package = "github.com/BeReal-App/backend-go/proto/entity/user/v1";

import "google/protobuf/timestamp.proto";
import "common/core/v1/common.proto";

message Streak {
    uint64 length = 1;
    common.core.v1.Date last_post_calendar_day = 2;
    google.protobuf.Timestamp updated_at = 3;
    string last_post_moment_id = 4;
}

message GetStreakRequest {
    string user_id = 1;
}

message GetStreakResponse {
    Streak streak = 1;
}

message GetStreaksRequest {
    repeated string user_ids = 1;
}

message GetStreaksResponse {
    // user_id -> streak
    // if user_id is not found, it will not be in the map
    map<string, Streak> streaks = 1;
}

message UpdateStreakRequest{
    string user_id = 1;
    uint64 length = 2;
    common.core.v1.Date last_post_calendar_day = 3;
    string last_post_moment_id = 4;
}

message UpdateStreakResponse{
    google.protobuf.Timestamp updated_at = 1;
}

// Streak Recovery Models
message StreakGap {
    common.core.v1.Date date = 1;
    string moment_id = 2;
}

message StreakRecoveryCalculation {
    uint64 current_streak = 1;
    uint64 estimated_streak = 2;
    repeated StreakGap gaps_to_fill = 3;
    bool is_eligible = 4;
    string error_message = 5;
}

message CalculateStreakRecoveryRequest {
    string user_id = 1;
    int32 number_of_days = 2; // Optional. If not set, defaults to 30 days.
}

message CalculateStreakRecoveryResponse {
    StreakRecoveryCalculation calculation = 1;
}

message ApplyStreakRecoveryRequest {
    string user_id = 1;
    repeated StreakGap gaps_to_fill = 2;
}

message ApplyStreakRecoveryResponse {
    Streak new_streak = 1;
}
