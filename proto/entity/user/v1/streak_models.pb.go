// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        (unknown)
// source: entity/user/v1/streak_models.proto

package v1

import (
	v1 "github.com/BeReal-App/backend-go/proto/common/core/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Streak struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Length              uint64                 `protobuf:"varint,1,opt,name=length,proto3" json:"length,omitempty"`
	LastPostCalendarDay *v1.Date               `protobuf:"bytes,2,opt,name=last_post_calendar_day,json=lastPostCalendarDay,proto3" json:"last_post_calendar_day,omitempty"`
	UpdatedAt           *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	LastPostMomentId    string                 `protobuf:"bytes,4,opt,name=last_post_moment_id,json=lastPostMomentId,proto3" json:"last_post_moment_id,omitempty"`
}

func (x *Streak) Reset() {
	*x = Streak{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Streak) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Streak) ProtoMessage() {}

func (x *Streak) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Streak.ProtoReflect.Descriptor instead.
func (*Streak) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{0}
}

func (x *Streak) GetLength() uint64 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *Streak) GetLastPostCalendarDay() *v1.Date {
	if x != nil {
		return x.LastPostCalendarDay
	}
	return nil
}

func (x *Streak) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Streak) GetLastPostMomentId() string {
	if x != nil {
		return x.LastPostMomentId
	}
	return ""
}

type GetStreakRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *GetStreakRequest) Reset() {
	*x = GetStreakRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStreakRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStreakRequest) ProtoMessage() {}

func (x *GetStreakRequest) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStreakRequest.ProtoReflect.Descriptor instead.
func (*GetStreakRequest) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{1}
}

func (x *GetStreakRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetStreakResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Streak *Streak `protobuf:"bytes,1,opt,name=streak,proto3" json:"streak,omitempty"`
}

func (x *GetStreakResponse) Reset() {
	*x = GetStreakResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStreakResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStreakResponse) ProtoMessage() {}

func (x *GetStreakResponse) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStreakResponse.ProtoReflect.Descriptor instead.
func (*GetStreakResponse) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{2}
}

func (x *GetStreakResponse) GetStreak() *Streak {
	if x != nil {
		return x.Streak
	}
	return nil
}

type GetStreaksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []string `protobuf:"bytes,1,rep,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
}

func (x *GetStreaksRequest) Reset() {
	*x = GetStreaksRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStreaksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStreaksRequest) ProtoMessage() {}

func (x *GetStreaksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStreaksRequest.ProtoReflect.Descriptor instead.
func (*GetStreaksRequest) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{3}
}

func (x *GetStreaksRequest) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

type GetStreaksResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user_id -> streak
	// if user_id is not found, it will not be in the map
	Streaks map[string]*Streak `protobuf:"bytes,1,rep,name=streaks,proto3" json:"streaks,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetStreaksResponse) Reset() {
	*x = GetStreaksResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStreaksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStreaksResponse) ProtoMessage() {}

func (x *GetStreaksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStreaksResponse.ProtoReflect.Descriptor instead.
func (*GetStreaksResponse) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{4}
}

func (x *GetStreaksResponse) GetStreaks() map[string]*Streak {
	if x != nil {
		return x.Streaks
	}
	return nil
}

type UpdateStreakRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId              string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Length              uint64   `protobuf:"varint,2,opt,name=length,proto3" json:"length,omitempty"`
	LastPostCalendarDay *v1.Date `protobuf:"bytes,3,opt,name=last_post_calendar_day,json=lastPostCalendarDay,proto3" json:"last_post_calendar_day,omitempty"`
	LastPostMomentId    string   `protobuf:"bytes,4,opt,name=last_post_moment_id,json=lastPostMomentId,proto3" json:"last_post_moment_id,omitempty"`
}

func (x *UpdateStreakRequest) Reset() {
	*x = UpdateStreakRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStreakRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStreakRequest) ProtoMessage() {}

func (x *UpdateStreakRequest) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStreakRequest.ProtoReflect.Descriptor instead.
func (*UpdateStreakRequest) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateStreakRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateStreakRequest) GetLength() uint64 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *UpdateStreakRequest) GetLastPostCalendarDay() *v1.Date {
	if x != nil {
		return x.LastPostCalendarDay
	}
	return nil
}

func (x *UpdateStreakRequest) GetLastPostMomentId() string {
	if x != nil {
		return x.LastPostMomentId
	}
	return ""
}

type UpdateStreakResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *UpdateStreakResponse) Reset() {
	*x = UpdateStreakResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStreakResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStreakResponse) ProtoMessage() {}

func (x *UpdateStreakResponse) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStreakResponse.ProtoReflect.Descriptor instead.
func (*UpdateStreakResponse) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateStreakResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Streak Recovery Models
type StreakGap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date     *v1.Date `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	MomentId string   `protobuf:"bytes,2,opt,name=moment_id,json=momentId,proto3" json:"moment_id,omitempty"`
}

func (x *StreakGap) Reset() {
	*x = StreakGap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreakGap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreakGap) ProtoMessage() {}

func (x *StreakGap) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreakGap.ProtoReflect.Descriptor instead.
func (*StreakGap) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{7}
}

func (x *StreakGap) GetDate() *v1.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *StreakGap) GetMomentId() string {
	if x != nil {
		return x.MomentId
	}
	return ""
}

type StreakRecoveryCalculation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentStreak   uint64       `protobuf:"varint,1,opt,name=current_streak,json=currentStreak,proto3" json:"current_streak,omitempty"`
	EstimatedStreak uint64       `protobuf:"varint,2,opt,name=estimated_streak,json=estimatedStreak,proto3" json:"estimated_streak,omitempty"`
	GapsToFill      []*StreakGap `protobuf:"bytes,3,rep,name=gaps_to_fill,json=gapsToFill,proto3" json:"gaps_to_fill,omitempty"`
	IsEligible      bool         `protobuf:"varint,4,opt,name=is_eligible,json=isEligible,proto3" json:"is_eligible,omitempty"`
	ErrorMessage    string       `protobuf:"bytes,5,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (x *StreakRecoveryCalculation) Reset() {
	*x = StreakRecoveryCalculation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreakRecoveryCalculation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreakRecoveryCalculation) ProtoMessage() {}

func (x *StreakRecoveryCalculation) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreakRecoveryCalculation.ProtoReflect.Descriptor instead.
func (*StreakRecoveryCalculation) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{8}
}

func (x *StreakRecoveryCalculation) GetCurrentStreak() uint64 {
	if x != nil {
		return x.CurrentStreak
	}
	return 0
}

func (x *StreakRecoveryCalculation) GetEstimatedStreak() uint64 {
	if x != nil {
		return x.EstimatedStreak
	}
	return 0
}

func (x *StreakRecoveryCalculation) GetGapsToFill() []*StreakGap {
	if x != nil {
		return x.GapsToFill
	}
	return nil
}

func (x *StreakRecoveryCalculation) GetIsEligible() bool {
	if x != nil {
		return x.IsEligible
	}
	return false
}

func (x *StreakRecoveryCalculation) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

type CalculateStreakRecoveryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId       string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	NumberOfDays int32  `protobuf:"varint,2,opt,name=number_of_days,json=numberOfDays,proto3" json:"number_of_days,omitempty"` // Optional. If not set, defaults to 30 days.
}

func (x *CalculateStreakRecoveryRequest) Reset() {
	*x = CalculateStreakRecoveryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateStreakRecoveryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateStreakRecoveryRequest) ProtoMessage() {}

func (x *CalculateStreakRecoveryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateStreakRecoveryRequest.ProtoReflect.Descriptor instead.
func (*CalculateStreakRecoveryRequest) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{9}
}

func (x *CalculateStreakRecoveryRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CalculateStreakRecoveryRequest) GetNumberOfDays() int32 {
	if x != nil {
		return x.NumberOfDays
	}
	return 0
}

type CalculateStreakRecoveryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Calculation *StreakRecoveryCalculation `protobuf:"bytes,1,opt,name=calculation,proto3" json:"calculation,omitempty"`
}

func (x *CalculateStreakRecoveryResponse) Reset() {
	*x = CalculateStreakRecoveryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateStreakRecoveryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateStreakRecoveryResponse) ProtoMessage() {}

func (x *CalculateStreakRecoveryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateStreakRecoveryResponse.ProtoReflect.Descriptor instead.
func (*CalculateStreakRecoveryResponse) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{10}
}

func (x *CalculateStreakRecoveryResponse) GetCalculation() *StreakRecoveryCalculation {
	if x != nil {
		return x.Calculation
	}
	return nil
}

type ApplyStreakRecoveryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string       `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GapsToFill []*StreakGap `protobuf:"bytes,2,rep,name=gaps_to_fill,json=gapsToFill,proto3" json:"gaps_to_fill,omitempty"`
}

func (x *ApplyStreakRecoveryRequest) Reset() {
	*x = ApplyStreakRecoveryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyStreakRecoveryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyStreakRecoveryRequest) ProtoMessage() {}

func (x *ApplyStreakRecoveryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyStreakRecoveryRequest.ProtoReflect.Descriptor instead.
func (*ApplyStreakRecoveryRequest) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{11}
}

func (x *ApplyStreakRecoveryRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ApplyStreakRecoveryRequest) GetGapsToFill() []*StreakGap {
	if x != nil {
		return x.GapsToFill
	}
	return nil
}

type ApplyStreakRecoveryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewStreak *Streak `protobuf:"bytes,1,opt,name=new_streak,json=newStreak,proto3" json:"new_streak,omitempty"`
}

func (x *ApplyStreakRecoveryResponse) Reset() {
	*x = ApplyStreakRecoveryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_entity_user_v1_streak_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyStreakRecoveryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyStreakRecoveryResponse) ProtoMessage() {}

func (x *ApplyStreakRecoveryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_entity_user_v1_streak_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyStreakRecoveryResponse.ProtoReflect.Descriptor instead.
func (*ApplyStreakRecoveryResponse) Descriptor() ([]byte, []int) {
	return file_entity_user_v1_streak_models_proto_rawDescGZIP(), []int{12}
}

func (x *ApplyStreakRecoveryResponse) GetNewStreak() *Streak {
	if x != nil {
		return x.NewStreak
	}
	return nil
}

var File_entity_user_v1_streak_models_proto protoreflect.FileDescriptor

var file_entity_user_v1_streak_models_proto_rawDesc = []byte{
	0x0a, 0x22, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f,
	0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xd5, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x12, 0x16, 0x0a,
	0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x49, 0x0a, 0x16, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x70, 0x6f,
	0x73, 0x74, 0x5f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x13, 0x6c, 0x61, 0x73,
	0x74, 0x50, 0x6f, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x44, 0x61, 0x79,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2d, 0x0a, 0x13, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x50, 0x6f,
	0x73, 0x74, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x2b, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x43, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x72, 0x65, 0x61, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x06,
	0x73, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x72, 0x65, 0x61, 0x6b, 0x52, 0x06, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x22, 0x2e, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0xb3, 0x01, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x73, 0x1a, 0x52,
	0x0a, 0x0c, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xc0, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x49, 0x0a, 0x16, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61,
	0x72, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x13, 0x6c, 0x61, 0x73, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x65, 0x6e,
	0x64, 0x61, 0x72, 0x44, 0x61, 0x79, 0x12, 0x2d, 0x0a, 0x13, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x70,
	0x6f, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x4d, 0x6f, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x51, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x52, 0x0a, 0x09, 0x53, 0x74, 0x72, 0x65,
	0x61, 0x6b, 0x47, 0x61, 0x70, 0x12, 0x28, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xf0, 0x01, 0x0a,
	0x19, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x43,
	0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61,
	0x6b, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x65, 0x73, 0x74,
	0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x12, 0x3b, 0x0a, 0x0c,
	0x67, 0x61, 0x70, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x66, 0x69, 0x6c, 0x6c, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x47, 0x61, 0x70, 0x52, 0x0a, 0x67,
	0x61, 0x70, 0x73, 0x54, 0x6f, 0x46, 0x69, 0x6c, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f,
	0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x69, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x5f, 0x0a, 0x1e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x74, 0x72, 0x65,
	0x61, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x44, 0x61, 0x79, 0x73,
	0x22, 0x6e, 0x0a, 0x1f, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b,
	0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x63, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x72, 0x0a, 0x1a, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x52,
	0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0c, 0x67, 0x61, 0x70, 0x73, 0x5f,
	0x74, 0x6f, 0x5f, 0x66, 0x69, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6b, 0x47, 0x61, 0x70, 0x52, 0x0a, 0x67, 0x61, 0x70, 0x73, 0x54, 0x6f,
	0x46, 0x69, 0x6c, 0x6c, 0x22, 0x54, 0x0a, 0x1b, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x52,
	0x09, 0x6e, 0x65, 0x77, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6b, 0x42, 0x37, 0x5a, 0x35, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x42, 0x65, 0x52, 0x65, 0x61, 0x6c, 0x2d,
	0x41, 0x70, 0x70, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2d, 0x67, 0x6f, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_entity_user_v1_streak_models_proto_rawDescOnce sync.Once
	file_entity_user_v1_streak_models_proto_rawDescData = file_entity_user_v1_streak_models_proto_rawDesc
)

func file_entity_user_v1_streak_models_proto_rawDescGZIP() []byte {
	file_entity_user_v1_streak_models_proto_rawDescOnce.Do(func() {
		file_entity_user_v1_streak_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_entity_user_v1_streak_models_proto_rawDescData)
	})
	return file_entity_user_v1_streak_models_proto_rawDescData
}

var file_entity_user_v1_streak_models_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_entity_user_v1_streak_models_proto_goTypes = []interface{}{
	(*Streak)(nil),                          // 0: entity.user.v1.Streak
	(*GetStreakRequest)(nil),                // 1: entity.user.v1.GetStreakRequest
	(*GetStreakResponse)(nil),               // 2: entity.user.v1.GetStreakResponse
	(*GetStreaksRequest)(nil),               // 3: entity.user.v1.GetStreaksRequest
	(*GetStreaksResponse)(nil),              // 4: entity.user.v1.GetStreaksResponse
	(*UpdateStreakRequest)(nil),             // 5: entity.user.v1.UpdateStreakRequest
	(*UpdateStreakResponse)(nil),            // 6: entity.user.v1.UpdateStreakResponse
	(*StreakGap)(nil),                       // 7: entity.user.v1.StreakGap
	(*StreakRecoveryCalculation)(nil),       // 8: entity.user.v1.StreakRecoveryCalculation
	(*CalculateStreakRecoveryRequest)(nil),  // 9: entity.user.v1.CalculateStreakRecoveryRequest
	(*CalculateStreakRecoveryResponse)(nil), // 10: entity.user.v1.CalculateStreakRecoveryResponse
	(*ApplyStreakRecoveryRequest)(nil),      // 11: entity.user.v1.ApplyStreakRecoveryRequest
	(*ApplyStreakRecoveryResponse)(nil),     // 12: entity.user.v1.ApplyStreakRecoveryResponse
	nil,                                     // 13: entity.user.v1.GetStreaksResponse.StreaksEntry
	(*v1.Date)(nil),                         // 14: common.core.v1.Date
	(*timestamppb.Timestamp)(nil),           // 15: google.protobuf.Timestamp
}
var file_entity_user_v1_streak_models_proto_depIdxs = []int32{
	14, // 0: entity.user.v1.Streak.last_post_calendar_day:type_name -> common.core.v1.Date
	15, // 1: entity.user.v1.Streak.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 2: entity.user.v1.GetStreakResponse.streak:type_name -> entity.user.v1.Streak
	13, // 3: entity.user.v1.GetStreaksResponse.streaks:type_name -> entity.user.v1.GetStreaksResponse.StreaksEntry
	14, // 4: entity.user.v1.UpdateStreakRequest.last_post_calendar_day:type_name -> common.core.v1.Date
	15, // 5: entity.user.v1.UpdateStreakResponse.updated_at:type_name -> google.protobuf.Timestamp
	14, // 6: entity.user.v1.StreakGap.date:type_name -> common.core.v1.Date
	7,  // 7: entity.user.v1.StreakRecoveryCalculation.gaps_to_fill:type_name -> entity.user.v1.StreakGap
	8,  // 8: entity.user.v1.CalculateStreakRecoveryResponse.calculation:type_name -> entity.user.v1.StreakRecoveryCalculation
	7,  // 9: entity.user.v1.ApplyStreakRecoveryRequest.gaps_to_fill:type_name -> entity.user.v1.StreakGap
	0,  // 10: entity.user.v1.ApplyStreakRecoveryResponse.new_streak:type_name -> entity.user.v1.Streak
	0,  // 11: entity.user.v1.GetStreaksResponse.StreaksEntry.value:type_name -> entity.user.v1.Streak
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_entity_user_v1_streak_models_proto_init() }
func file_entity_user_v1_streak_models_proto_init() {
	if File_entity_user_v1_streak_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_entity_user_v1_streak_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Streak); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_entity_user_v1_streak_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStreakRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_entity_user_v1_streak_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStreakResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_entity_user_v1_streak_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStreaksRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_entity_user_v1_streak_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStreaksResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_entity_user_v1_streak_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStreakRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_entity_user_v1_streak_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStreakResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_entity_user_v1_streak_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreakGap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_entity_user_v1_streak_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreakRecoveryCalculation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_entity_user_v1_streak_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculateStreakRecoveryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_entity_user_v1_streak_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculateStreakRecoveryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_entity_user_v1_streak_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyStreakRecoveryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_entity_user_v1_streak_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyStreakRecoveryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_entity_user_v1_streak_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_entity_user_v1_streak_models_proto_goTypes,
		DependencyIndexes: file_entity_user_v1_streak_models_proto_depIdxs,
		MessageInfos:      file_entity_user_v1_streak_models_proto_msgTypes,
	}.Build()
	File_entity_user_v1_streak_models_proto = out.File
	file_entity_user_v1_streak_models_proto_rawDesc = nil
	file_entity_user_v1_streak_models_proto_goTypes = nil
	file_entity_user_v1_streak_models_proto_depIdxs = nil
}
