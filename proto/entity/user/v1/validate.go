package v1

import (
	"errors"
	"fmt"

	v1 "github.com/BeReal-App/backend-go/proto/common/core/v1"
	"github.com/BeReal-App/backend-go/shared/util"
)

const (
	minQueryLength = 3
	maxBatchSize   = 300
	maxLimit       = 100
	maxFilterSize  = 1000
)

var (
	errCannotBeNilOrEmpty     = errors.New("cannot be nil or empty")
	errUpdateOrClearMustBeSet = errors.New("either update or clear field must be populated")
	errOneStructMustBeSet     = errors.New("one of the user substruct must be set")
	errOneWhereMustBeSet      = errors.New("one of the where clause must be set (user_id, username, etc)")
	errMaxBatchSize           = fmt.Errorf("batch size can not exceed %d", maxBatchSize)
	errMaxLimit               = fmt.Errorf("limit can not exceed %d", maxLimit)
	errMaxFilterSize          = fmt.Errorf("filter size can not exceed %d", maxFilterSize)
)

func (r *GetOAUsersRequest) Validate() error {
	return nil
}

func (r *GetUserRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UserId == "" && r.Username == "" && r.PhoneNumber == "" {
		return errOneWhereMustBeSet
	}
	if r.Structs == nil {
		return fmt.Errorf("structs %w", errCannotBeNilOrEmpty)
	}
	if !r.Structs.Profile {
		return errOneStructMustBeSet
	}
	return nil
}

func (r *GenUsernamesRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.DesiredUsername == "" {
		return fmt.Errorf("desired_username %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (r *SearchUsersRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.Query == "" {
		return fmt.Errorf("query %w", errCannotBeNilOrEmpty)
	}

	if r.Limit < 1 {
		return fmt.Errorf("limit %w", errCannotBeNilOrEmpty)
	}

	if r.Limit > maxLimit {
		return fmt.Errorf("limit %w", errMaxLimit)
	}

	if len(r.ExcludeUserIds) > maxFilterSize {
		return errMaxLimit
	}

	if len(r.IncludeUserIds) > maxFilterSize {
		return errMaxFilterSize
	}

	return nil
}

func (r *UpdateUserRequest) Validate() error {
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	if r.Update == nil && r.Clear == nil {
		return errUpdateOrClearMustBeSet
	}
	if r.Update != nil && r.Update.ProfilePicture != nil {
		if err := r.Update.ProfilePicture.Validate(); err != nil {
			return err
		}
	}
	return nil
}

func (r *UpdateUserActivityRequest) Validate() error {
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}

	return nil
}

func (r *GetUserActivityRequest) Validate() error {
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}

	return nil
}

func (r *DeleteUserRequest) Validate() error {
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (r *InsertUserRequest) Validate() error {
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (p *ProfilePicture) Validate() error {
	if p.Path == "" {
		return fmt.Errorf("path %w", errCannotBeNilOrEmpty)
	}
	if p.Bucket == "" {
		return fmt.Errorf("bucket %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (r *GetUserBasicInfoRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	return nil
}

func (r *GetUserBasicInfoBatchRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}

	if len(r.UserIds) > maxBatchSize {
		return errMaxBatchSize
	}
	for i, ID := range r.UserIds {
		if ID == "" {
			return fmt.Errorf("user_ids[%d] %w", i, errCannotBeNilOrEmpty)
		}
	}
	return nil
}

func (r *UserExistsRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (r *GetUserRealmojisRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (r *UserRealmojiExistsRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	if r.Type == v1.Emoji_TYPE_UNSPECIFIED {
		return fmt.Errorf("type %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (r *DisableUserRealmojiRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	if r.Id == "" {
		return fmt.Errorf("id %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (r *InsertUserRealmojiRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UpdatedAt == nil {
		return fmt.Errorf("updated_at %w", errCannotBeNilOrEmpty)
	}
	if err := r.Realmoji.Validate(); err != nil {
		return fmt.Errorf("realmoji: %w", err)
	}
	if r.Realmoji.CreatedAt == nil {
		return fmt.Errorf("realmoji.created_at %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (r *AddUserRealmojiRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	if r.Media == nil {
		return fmt.Errorf("media %w", errCannotBeNilOrEmpty)
	}
	if r.Type == v1.Emoji_TYPE_UNSPECIFIED {
		return fmt.Errorf("media %w", errCannotBeNilOrEmpty)
	}
	if err := r.Media.Validate(); err != nil {
		return fmt.Errorf("media: %w", err)
	}
	return nil
}

func (r *Realmoji) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.Id == "" {
		return fmt.Errorf("id %w", errCannotBeNilOrEmpty)
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	if r.Media == nil {
		return fmt.Errorf("media %w", errCannotBeNilOrEmpty)
	}
	if r.Type == v1.Emoji_TYPE_UNSPECIFIED {
		return fmt.Errorf("media %w", errCannotBeNilOrEmpty)
	}
	if err := r.Media.Validate(); err != nil {
		return fmt.Errorf("media: %w", err)
	}
	return nil
}

func (r *DeleteUserRealmojiRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.Id == "" {
		return fmt.Errorf("id %w", errCannotBeNilOrEmpty)
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (r *GetStreakRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}

	return nil
}

func (r *GetStreaksRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if len(r.UserIds) == 0 {
		return fmt.Errorf("user_ids %w", errCannotBeNilOrEmpty)
	}
	for i, ID := range r.UserIds {
		if ID == "" {
			return fmt.Errorf("user_ids[%d] %w", i, errCannotBeNilOrEmpty)
		}
	}
	return nil
}

func (r *UpdateStreakRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}

	return nil
}

func (r *FindUsersByUsernameRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if len(r.Usernames) == 0 {
		return fmt.Errorf("usernames %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (r *MatchUserIDsWithHashedPhoneNumbersRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	return nil
}

func (r *GetUnmatchedHashedPhoneNumbersRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	for i, hash := range r.GetHashedPhoneNumbers() {
		if !util.ValidateHashedPhoneNumber(hash) {
			return fmt.Errorf("invalid hash hashed_phone_numbers[%d]", i)
		}
	}
	return nil
}

func (r *GetUserInterestRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (r *PutUserInterestRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	return nil
}

func (r *CalculateStreakRecoveryRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	if r.NumberOfDays < 0 {
		return fmt.Errorf("number_of_days must be positive or zero (unset)")
	}
	return nil
}

func (r *ApplyStreakRecoveryRequest) Validate() error {
	if r == nil {
		return errCannotBeNilOrEmpty
	}
	if r.UserId == "" {
		return fmt.Errorf("user_id %w", errCannotBeNilOrEmpty)
	}
	if len(r.GapsToFill) == 0 {
		return fmt.Errorf("gaps_to_fill %w", errCannotBeNilOrEmpty)
	}
	if len(r.GapsToFill) > 10 {
		return fmt.Errorf("too many gaps to fill: %d (max 10)", len(r.GapsToFill))
	}

	// Validate each gap
	for i, gap := range r.GapsToFill {
		if gap.Date == nil {
			return fmt.Errorf("gaps_to_fill[%d].date %w", i, errCannotBeNilOrEmpty)
		}
		if gap.MomentId == "" {
			return fmt.Errorf("gaps_to_fill[%d].moment_id %w", i, errCannotBeNilOrEmpty)
		}
	}

	return nil
}
