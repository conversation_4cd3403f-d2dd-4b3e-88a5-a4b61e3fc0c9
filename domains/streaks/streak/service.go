package streak

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	_ "time/tzdata" // required for time.LoadLocation to work with IANA timezone names

	gps "cloud.google.com/go/pubsub"
	gSpanner "cloud.google.com/go/spanner"
	"github.com/BeReal-App/backend-go/domains/entity/moment"
	"github.com/BeReal-App/backend-go/domains/entity/moment-grpc-api/models"
	"github.com/BeReal-App/backend-go/domains/streaks/events"
	"github.com/BeReal-App/backend-go/domains/streaks/streak/internal"
	pbMoment "github.com/BeReal-App/backend-go/proto/entity/moment/v1"
	pbUser "github.com/BeReal-App/backend-go/proto/entity/user/v1"
	"github.com/BeReal-App/backend-go/shared/bpubsub/publish"
	"github.com/BeReal-App/backend-go/shared/model"
	"github.com/rs/zerolog"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type (
	ArchivePostSpanner *gSpanner.Client
	EntityPostSpanner  *gSpanner.Client
	EntityUserSpanner  *gSpanner.Client
)

// if user keeps the post for at least this number of hours before deleting,
// it will be counted in the streak because it's a memory deletion
const minKeepHoursBeforeDelete = 4

type Service struct {
	user               pbUser.UserServiceClient
	momentCache        *moment.Client
	moment             pbMoment.MomentServiceClient
	archivePost        *gSpanner.Client
	entityPost         *gSpanner.Client
	entityUser         *gSpanner.Client
	streakUpdatedTopic *gps.Topic
	recentMoments      map[string]models.Moment // useful during a moment when we have a lot of requests and we might not have an updated moment cache
}

func NewStreakService(
	userServiceClient pbUser.UserServiceClient,
	momentCache *moment.Client,
	moment pbMoment.MomentServiceClient,
	archivePost ArchivePostSpanner,
	entityPost EntityPostSpanner,
	entityUser EntityUserSpanner,
	pubsubClient *gps.Client,
) *Service {
	streakUpdatedTopic := pubsubClient.Topic(events.StreakUpdateEventTopic)
	return &Service{
		user:               userServiceClient,
		momentCache:        momentCache,
		moment:             moment,
		archivePost:        archivePost,
		entityPost:         entityPost,
		entityUser:         entityUser,
		streakUpdatedTopic: streakUpdatedTopic,
		recentMoments:      make(map[string]models.Moment),
	}
}

func (s *Service) UpdateStreak(ctx context.Context, userID string, newPostMomentID *string, deletedPostID *string, computeIfMissing bool) (*pbUser.Streak, error) {
	info, err := s.user.GetUserBasicInfo(ctx, &pbUser.GetUserBasicInfoRequest{UserId: userID})
	if info == nil || err != nil {
		return nil, fmt.Errorf("on GetUserBasicInfo for userID %s: %w", userID, err)
	}
	basicInfo := info.BasicInfo

	currentUserMoment := s.momentCache.LastFiredIn(model.Region(basicInfo.Region))

	// Check if we got a valid moment (not empty)
	if currentUserMoment.ID == "" {
		zerolog.Ctx(ctx).Warn().
			Str("user_id", userID).
			Str("region", basicInfo.Region).
			Msg("No moment found for region in moment cache - cannot update streak")
		return nil, fmt.Errorf("no moment found for region %s", basicInfo.Region)
	}

	if newPostMomentID != nil && *newPostMomentID != currentUserMoment.ID {
		newPostMoment, err := s.momentFromID(ctx, *newPostMomentID)
		if err != nil {
			return nil, fmt.Errorf("on momentFromID for momentID %s: %w", *newPostMomentID, err)
		}

		if newPostMoment.Before(currentUserMoment) {
			return nil, nil
		}

		if currentUserMoment.Before(newPostMoment) {
			currentUserMoment = newPostMoment
		}
	}

	mostRecentMomentCalendarDay, err := internal.CalendarDateInTimezone(currentUserMoment)
	if err != nil {
		return nil, fmt.Errorf("on CalendarDateInTimezone for currentUserMoment: %w", err)
	}

	mostRecentUserPostMomentCalendarDay, err := s.mostRecentUserPostMomentCalendarDay(ctx, userID, newPostMomentID, deletedPostID)
	if err != nil {
		return nil, fmt.Errorf("on mostRecentUserPostMomentCalendarDay for userID %s: %w", userID, err)
	}

	var currentStreak *pbUser.Streak
	streakResponse, err := s.user.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: userID})
	if st, ok := status.FromError(err); ok && st.Code() == codes.NotFound {
		internal.StreakMissingCounter.WithLabelValues(
			basicInfo.Region,
			mostRecentMomentCalendarDay.Weekday().String(),
		).Inc()
		if computeIfMissing {
			streak, err := s.ComputeStreak(ctx, &events.StreakRecomputeEvent{
				UserID:  userID,
				Region:  basicInfo.Region,
				DryMode: false,
			})
			if err != nil {
				return nil, fmt.Errorf("on ComputeStreak for userID %s: %w", userID, err)
			}
			currentStreak = streak
		} else {
			return nil, nil
		}
	} else if err != nil {
		return nil, fmt.Errorf("on GetStreak for userID %s: %w", userID, err)
	} else {
		currentStreak = streakResponse.Streak
	}

	newStreak := internal.IncrementStreak(currentStreak, mostRecentMomentCalendarDay, mostRecentUserPostMomentCalendarDay)

	_, err = s.user.UpdateStreak(ctx, &pbUser.UpdateStreakRequest{
		UserId:              userID,
		Length:              newStreak.Length,
		LastPostCalendarDay: newStreak.LastPostCalendarDay,
		LastPostMomentId:    newStreak.LastPostMomentId,
	})
	if err != nil {
		return nil, fmt.Errorf("on UpdateStreak for userID %s: %w", userID, err)
	}

	if newStreak.Length > currentStreak.Length {
		internal.StreakCounter.WithLabelValues(
			basicInfo.Region,
			newStreak.LastPostCalendarDay.Time().Weekday().String(),
		).Inc()
		internal.StreakLengthCounter.WithLabelValues(
			basicInfo.Region,
			newStreak.LastPostCalendarDay.Time().Weekday().String(),
		).Add(float64(newStreak.Length))
	}

	emitErr := s.emitStreakUpdateEvent(ctx, userID, newStreak)
	if emitErr != nil {
		return nil, fmt.Errorf("on emitStreakUpdateEvent for userID %s: %w", userID, emitErr)
	}

	return newStreak, nil
}

func (s *Service) ComputeStreak(ctx context.Context, event *events.StreakRecomputeEvent) (*pbUser.Streak, error) {
	userID := event.UserID
	region := event.Region
	dryMode := event.DryMode

	l := zerolog.Ctx(ctx)

	l.Info().
		Str("user_id", userID).
		Str("raw_region", region).
		Msg("ComputeStreak: Starting streak computation")

	// Additional validation to prevent processing invalid data
	if userID == "" || userID == "EVERYONE" {
		return nil, fmt.Errorf("invalid user ID: %s", userID)
	}

	regionCode := model.Region(region)
	if region == "" || regionCode == model.RegionUnknown {
		return nil, fmt.Errorf("unknown region %s for user %s", region, userID)
	}

	l.Info().
		Str("user_id", userID).
		Str("raw_region", region).
		Str("parsed_region_code", string(regionCode)).
		Msg("ComputeStreak: Region parsing completed")

	// Get the current streak before recomputation for comparison
	var currentStreak *pbUser.Streak
	streakResponse, err := s.user.GetUserBasicInfo(ctx, &pbUser.GetUserBasicInfoRequest{UserId: userID})
	if err != nil {
		return nil, fmt.Errorf("on GetUserBasicInfo for userID %s: %w", userID, err)
	}
	if streakResponse == nil || streakResponse.BasicInfo == nil {
		return nil, fmt.Errorf("no user basic info found for userID %s", userID)
	}

	// Verify the region matches
	if streakResponse.BasicInfo.Region != region {
		l.Warn().
			Str("event_region", region).
			Str("user_region", streakResponse.BasicInfo.Region).
			Str("user_id", userID).
			Msg("ComputeStreak: Region mismatch between event and user data")
		// Use the user's actual region instead of the event region
		regionCode = model.Region(streakResponse.BasicInfo.Region)
	}

	streakResponse2, err := s.user.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: userID})
	if err != nil {
		if st, ok := status.FromError(err); ok && st.Code() == codes.NotFound {
			// No existing streak found, this is expected for new users
			currentStreak = &pbUser.Streak{Length: 0, LastPostCalendarDay: nil, LastPostMomentId: ""}
		} else {
			return nil, fmt.Errorf("on GetStreak for userID %s: %w", userID, err)
		}
	} else {
		currentStreak = streakResponse2.Streak
	}

	// get calendar date of most recent moment in user's region
	// this is the reference date for the streak
	currentUserMoment := s.momentCache.LastFiredIn(regionCode)

	// Check if we got a valid moment (not empty)
	if currentUserMoment.ID == "" {
		l.Warn().
			Str("user_id", userID).
			Str("region", region).
			Msg("No moment found for region in moment cache - cannot compute streak")
		return nil, fmt.Errorf("no moment found for region %s", region)
	}

	mostRecentMomentCalendarDay, err := internal.CalendarDateInTimezone(currentUserMoment)
	if err != nil {
		return nil, fmt.Errorf("on CalendarDateInTimezone: %w", err)
	}

	postDays, err := s.getPostCalendarDaysFromSpanner(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("on getPostCalendarDaysFromSpanner (entity post): %w", err)
	}

	newStreak := internal.ComputeStreak(mostRecentMomentCalendarDay, postDays)

	// Compare old and new streaks and log warning if different
	if currentStreak != nil && (currentStreak.Length != newStreak.Length ||
		!streaksHaveSameLastPostDay(currentStreak, newStreak)) {

		oldLastPostDay := "nil"
		if currentStreak.LastPostCalendarDay != nil {
			oldLastPostDay = currentStreak.LastPostCalendarDay.Time().Format("2006-01-02")
		}

		newLastPostDay := "nil"
		if newStreak.LastPostCalendarDay != nil {
			newLastPostDay = newStreak.LastPostCalendarDay.Time().Format("2006-01-02")
		}

		logEvent := l.Warn().
			Str("user_id", userID).
			Str("region", region).
			Uint64("old_streak_length", currentStreak.Length).
			Uint64("new_streak_length", newStreak.Length).
			Str("old_last_post_day", oldLastPostDay).
			Str("new_last_post_day", newLastPostDay).
			Str("old_last_post_moment_id", currentStreak.LastPostMomentId).
			Str("new_last_post_moment_id", newStreak.LastPostMomentId).
			Bool("dry_mode", dryMode)

		var message string
		if newStreak.Length == 0 && currentStreak.Length > 0 {
			// Streak reset to 0
			if dryMode {
				message = "Streak recomputation would RESET streak to 0 (DRY MODE - no changes applied)"
			} else {
				message = "Streak recomputation RESET streak to 0"
			}
		} else if newStreak.Length < currentStreak.Length {
			// Streak decreased (but not to 0)
			if dryMode {
				message = "Streak recomputation would DECREASE streak length (DRY MODE - no changes applied)"
			} else {
				message = "Streak recomputation DECREASED streak length"
			}
		} else {
			// Streak increased or changed in other ways (e.g. same length but different last post day)
			// Since the outer 'if' already filters out normal increments, this 'else' covers significant changes.
			if dryMode {
				message = "Streak recomputation would result in different streak value (DRY MODE - no changes applied)"
			} else {
				message = "Streak recomputation resulted in different streak value"
			}
		}

		logEvent.Msg(message)

		// Increment the metric for tracking streak differences
		if newStreak.Length == 0 && currentStreak.Length > 0 {
			internal.StreakRecomputationResetCounter.WithLabelValues(
				region,
				mostRecentMomentCalendarDay.Weekday().String(),
			).Inc()
		} else if newStreak.Length < currentStreak.Length {
			internal.StreakRecomputationDecreaseCounter.WithLabelValues(
				region,
				mostRecentMomentCalendarDay.Weekday().String(),
			).Inc()
		} else if newStreak.Length > currentStreak.Length {
			internal.StreakRecomputationIncreaseCounter.WithLabelValues(
				region,
				mostRecentMomentCalendarDay.Weekday().String(),
			).Inc()
		}
	}

	// Skip actual database update in dry mode
	if dryMode {
		l.Warn().
			Str("user_id", userID).
			Str("region", region).
			Uint64("streak_length", newStreak.Length).
			Bool("dry_mode", true).
			Msg("Streak computation completed in dry mode - no database changes applied")
		return newStreak, nil
	}

	_, err = s.user.UpdateStreak(ctx, &pbUser.UpdateStreakRequest{
		UserId:              userID,
		Length:              newStreak.Length,
		LastPostCalendarDay: newStreak.LastPostCalendarDay,
		LastPostMomentId:    newStreak.LastPostMomentId,
	})
	if err != nil {
		return nil, fmt.Errorf("on UpdateStreak for userID %s: %w", userID, err)
	}

	if newStreak.Length > 0 && newStreak.LastPostCalendarDay != nil {
		internal.StreakCounter.WithLabelValues(
			region,
			newStreak.LastPostCalendarDay.Time().Weekday().String(),
		).Inc()
		internal.StreakLengthCounter.WithLabelValues(
			region,
			newStreak.LastPostCalendarDay.Time().Weekday().String(),
		).Add(float64(newStreak.Length))
		internal.StreakComputedCounter.WithLabelValues(
			region,
			newStreak.LastPostCalendarDay.Time().Weekday().String(),
		).Inc()
	}

	return newStreak, s.emitStreakUpdateEvent(ctx, userID, newStreak)
}

func (s *Service) getPostCalendarDaysFromSpanner(ctx context.Context, userID string) ([]internal.CalendarDayWithMoment, error) {
	var allDaysWithMoments []internal.CalendarDayWithMoment

	mostRecentUserPostMomentCalendarDays, err := s.getRecentUserPostsCalendarDays(ctx, userID, nil)
	if err != nil {
		return nil, fmt.Errorf("on mostRecentUserPostMomentCalendarDays: %w", err)
	}
	allDaysWithMoments = append(allDaysWithMoments, mostRecentUserPostMomentCalendarDays...)

	deletedMemoriesCalendarDays, err := s.getDeletedMemoriesCalendarDays(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("on getDeletedMemoriesCalendarDays: %w", err)
	}
	allDaysWithMoments = append(allDaysWithMoments, deletedMemoriesCalendarDays...)

	// Get streak compensation calendar days
	streakCompensationCalendarDays, err := s.getStreakCompensationCalendarDays(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("on getStreakCompensationCalendarDays: %w", err)
	}
	allDaysWithMoments = append(allDaysWithMoments, streakCompensationCalendarDays...)

	postsCalendarDays, err := s.getPostsCalendarDaysFromEntityPost(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("on getPostsCalendarDaysFromEntityPost: %w", err)
	}
	allDaysWithMoments = append(allDaysWithMoments, postsCalendarDays...)

	return allDaysWithMoments, nil
}

func (s *Service) momentFromID(ctx context.Context, momentID string) (models.Moment, error) {
	l := zerolog.Ctx(ctx)

	l.Debug().
		Str("momentID", momentID).
		Msg("momentFromID: Attempting to load moment")

	if moment, ok := s.recentMoments[momentID]; ok {
		l.Debug().
			Str("momentID", momentID).
			Str("source", "recentMoments").
			Bool("hasTimestamps", moment.FiredAt != nil || moment.ScheduledAt != nil).
			Str("timezone", moment.Timezone).
			Msg("momentFromID: Found moment in recentMoments cache")
		return moment, nil
	}

	if moment, ok := s.momentCache.GetAll().Get(momentID); ok {
		l.Debug().
			Str("momentID", momentID).
			Str("source", "momentCache").
			Bool("hasTimestamps", moment.FiredAt != nil || moment.ScheduledAt != nil).
			Str("timezone", moment.Timezone).
			Msg("momentFromID: Found moment in momentCache")
		return moment, nil
	}

	l.Debug().
		Str("momentID", momentID).
		Msg("momentFromID: Moment not found in cache, fetching from gRPC")

	moment, err := s.fetchRecentMoment(ctx, momentID)
	if err == nil {
		l.Debug().
			Str("momentID", momentID).
			Str("source", "fetchRecentMoment").
			Bool("hasTimestamps", moment.FiredAt != nil || moment.ScheduledAt != nil).
			Str("timezone", moment.Timezone).
			Msg("momentFromID: Successfully fetched recent moment")
		return moment, nil
	}

	l.Debug().
		Str("momentID", momentID).
		Err(err).
		Msg("momentFromID: Failed to fetch recent moment, trying future moments")

	moment, err = s.fetchFutureMoment(ctx, momentID)
	if err != nil {
		l.Error().
			Str("momentID", momentID).
			Err(err).
			Msg("momentFromID: Failed to fetch future moment")
		return models.Moment{}, fmt.Errorf("on fetchFutureMoment for momentID %s: %w", momentID, err)
	}

	l.Debug().
		Str("momentID", momentID).
		Str("source", "fetchFutureMoment").
		Bool("hasTimestamps", moment.FiredAt != nil || moment.ScheduledAt != nil).
		Str("timezone", moment.Timezone).
		Msg("momentFromID: Successfully fetched future moment")

	return moment, nil
}

func (s *Service) fetchRecentMoment(ctx context.Context, momentID string) (models.Moment, error) {
	response, err := s.moment.GetLastNMoments(ctx, &pbMoment.GetLastNMomentsRequest{NumMoments: 1})
	if err != nil {
		return models.Moment{}, fmt.Errorf("on GetLastNMoments: %w", err)
	}

	for _, r := range response.RegionMoments {
		for _, m := range r.Moments {
			if m.Id.String() == momentID {
				moment := models.FromProto(m)
				s.recentMoments[momentID] = moment
				return moment, nil
			}
		}
	}

	return models.Moment{}, fmt.Errorf("unable to find moment %s", momentID)
}

func (s *Service) fetchFutureMoment(ctx context.Context, momentID string) (models.Moment, error) {
	response, err := s.moment.GetAllMoments(ctx, &pbMoment.GetAllMomentsRequest{
		LimitPerRegion:       25,
		IncludeFutureMoments: true,
	})
	if err != nil {
		return models.Moment{}, fmt.Errorf("on GetAllMoments: %w", err)
	}

	for _, r := range response.RegionMoments {
		for _, m := range r.Moments {
			if m.Id.String() == momentID {
				return models.FromProto(m), nil
			}
		}
	}

	return models.Moment{}, fmt.Errorf("unable to find moment %s", momentID)
}

func (s *Service) mostRecentUserPostMomentCalendarDay(ctx context.Context, userID string, newPostMomentID *string, deletedPostID *string) (internal.CalendarDayWithMoment, error) {
	var mostRecentPostMoment *internal.CalendarDayWithMoment
	if newPostMomentID != nil {
		newPostMomentDay, err := s.postCalendarDay(ctx, *newPostMomentID)
		if err != nil {
			return internal.CalendarDayWithMoment{}, fmt.Errorf("on postCalendarDay for momentID %s: %w", *newPostMomentID, err)
		}
		mostRecentPostMoment = &internal.CalendarDayWithMoment{
			CalendarDay: newPostMomentDay,
			MomentID:    *newPostMomentID,
		}
	}

	recentPostMoments, err := s.getRecentUserPostsCalendarDays(ctx, userID, deletedPostID)
	if err != nil {
		return internal.CalendarDayWithMoment{}, fmt.Errorf("on getRecentUserPostsCalendarDays for userID %s (newPostMomentID: %v, deletedPostID: %v): %w",
			userID,
			formatNullableString(newPostMomentID),
			formatNullableString(deletedPostID),
			err)
	}

	for _, dayWithMoment := range recentPostMoments {
		if mostRecentPostMoment == nil || mostRecentPostMoment.CalendarDay.Before(dayWithMoment.CalendarDay) {
			mostRecentPostMoment = &dayWithMoment
		}
	}

	if mostRecentPostMoment == nil {
		return internal.CalendarDayWithMoment{}, nil
	}

	return *mostRecentPostMoment, nil
}

func (s *Service) postCalendarDay(ctx context.Context, postMomentID string) (time.Time, error) {
	postMoment, err := s.momentFromID(ctx, postMomentID)
	if err != nil {
		return time.Time{}, fmt.Errorf("on momentFromID for postMomentID %s: %w", postMomentID, err)
	}
	calendarDay, err := internal.CalendarDateInTimezone(postMoment)
	if err != nil {
		return time.Time{}, fmt.Errorf("on CalendarDateInTimezone for postMoment: %w", err)
	}
	return calendarDay, nil
}

func (s *Service) getDeletedMemoriesCalendarDays(ctx context.Context, userID string) (res []internal.CalendarDayWithMoment, err error) {
	iter := s.archivePost.Single().Query(
		ctx,
		gSpanner.Statement{
			SQL: `SELECT DISTINCT momentId FROM memories_deletions WHERE userId = @userID`,
			Params: map[string]interface{}{
				"userID": userID,
			},
		},
	)
	defer iter.Stop()
	return res, iter.Do(func(r *gSpanner.Row) error {
		var momentID gSpanner.NullString
		if err := r.Columns(&momentID); err != nil {
			return fmt.Errorf("on r.Columns: %w", err)
		}
		if !momentID.Valid {
			// we don't have momentIDs for memories deletions that are before november 2022
			return nil
		}
		calendarDay, err := s.postCalendarDay(ctx, momentID.StringVal)
		if err != nil {
			return fmt.Errorf("on postCalendarDay for momentID %s: %w", momentID.StringVal, err)
		}
		res = append(res, internal.CalendarDayWithMoment{
			CalendarDay: calendarDay,
			MomentID:    momentID.StringVal,
		})
		return nil
	})
}

func (s *Service) getPostsCalendarDaysFromEntityPost(ctx context.Context, userID string) (res []internal.CalendarDayWithMoment, err error) {
	iter := s.entityPost.Single().Query(
		ctx,
		gSpanner.Statement{
			SQL: `SELECT DISTINCT momentId FROM posts WHERE userId = @userID AND (deletedAt IS NULL OR TIMESTAMP_DIFF(deletedAt, createdAt, HOUR) >= @minHours)`,
			Params: map[string]interface{}{
				"userID":   userID,
				"minHours": minKeepHoursBeforeDelete,
			},
		},
	)
	defer iter.Stop()
	return res, iter.Do(func(r *gSpanner.Row) error {
		var momentID gSpanner.NullString
		if err := r.Columns(&momentID); err != nil {
			return fmt.Errorf("on r.Columns: %w", err)
		}
		if !momentID.Valid { // ignore posts that don't have a momentID
			return nil
		}
		calendarDay, err := s.postCalendarDay(ctx, momentID.StringVal)
		if err != nil {
			return fmt.Errorf("on postCalendarDay: %w", err)
		}
		res = append(res, internal.CalendarDayWithMoment{
			CalendarDay: calendarDay,
			MomentID:    momentID.StringVal,
		})
		return nil
	})
}

func (s *Service) getRecentUserPostsCalendarDays(ctx context.Context, userID string, excludePostID *string) (res []internal.CalendarDayWithMoment, err error) {
	where := "userId = @userID AND (deletedAt IS NULL OR TIMESTAMP_DIFF(deletedAt, createdAt, HOUR) >= @minHours)"
	params := map[string]interface{}{
		"userID":   userID,
		"minHours": minKeepHoursBeforeDelete,
	}

	if excludePostID != nil {
		where += " AND postId != @excludePostID"
		params["excludePostID"] = *excludePostID
	}

	sql := fmt.Sprintf(`SELECT momentId FROM user_posts WHERE %s ORDER BY CreatedAt DESC LIMIT 10`, where)

	iter := s.entityPost.Single().Query(
		ctx,
		gSpanner.Statement{
			SQL:    sql,
			Params: params,
		},
	)

	defer iter.Stop()
	return res, iter.Do(func(r *gSpanner.Row) error {
		var momentID gSpanner.NullString
		if err := r.Columns(&momentID); err != nil {
			return fmt.Errorf("on r.Columns: %w", err)
		}
		if !momentID.Valid { // ignore posts that don't have a momentID
			return nil
		}

		calendarDay, err := s.postCalendarDay(ctx, momentID.StringVal)
		if err != nil {
			return fmt.Errorf("on postCalendarDay: %w", err)
		}
		res = append(res, internal.CalendarDayWithMoment{
			CalendarDay: calendarDay,
			MomentID:    momentID.StringVal,
		})
		return nil
	})
}

// Helper function for error formatting
func formatNullableString(s *string) string {
	if s == nil {
		return "nil"
	}
	return *s
}

func (s *Service) emitStreakUpdateEvent(ctx context.Context, userID string, streak *pbUser.Streak) error {
	lastPostCalendarDayTime := time.Time{}
	if streak.LastPostCalendarDay != nil {
		lastPostCalendarDayTime = streak.LastPostCalendarDay.Time()
	}
	data, err := json.Marshal(&events.StreakUpdateEvent{
		UserID:              userID,
		StreakLength:        int(streak.Length),
		LastPostCalendarDay: lastPostCalendarDayTime,
	})
	if err != nil {
		return fmt.Errorf("on json.Unmarshal: %w", err)
	}

	publishFunc := publish.WithRetry(s.streakUpdatedTopic)
	if _, err := publishFunc(ctx, &gps.Message{Data: data}); err != nil {
		return fmt.Errorf("on publishFunc: %w", err)
	}
	return nil
}

func (s *Service) getStreakCompensationCalendarDays(ctx context.Context, userID string) (res []internal.CalendarDayWithMoment, err error) {
	iter := s.entityUser.Single().QueryWithOptions(
		ctx,
		gSpanner.Statement{
			SQL: `SELECT Date, MomentID FROM StreakCompensation WHERE UserId = @userID`,
			Params: map[string]interface{}{
				"userID": userID,
			},
		},
		gSpanner.QueryOptions{
			RequestTag: "getStreakCompensationCalendarDays",
		},
	)
	defer iter.Stop()
	return res, iter.Do(func(r *gSpanner.Row) error {
		var date gSpanner.NullDate
		var momentID gSpanner.NullString
		if err := r.Columns(&date, &momentID); err != nil {
			return fmt.Errorf("on r.Columns: %w", err)
		}

		if !date.Valid {
			return nil
		}

		// Convert Date to time.Time
		calendarDay := time.Date(date.Date.Year, time.Month(date.Date.Month), date.Date.Day, 0, 0, 0, 0, time.UTC)

		// Use MomentID if available, otherwise empty string for backward compatibility
		momentIDStr := ""
		if momentID.Valid {
			momentIDStr = momentID.StringVal
		} else {
			// Log a warning if moment ID is empty - this indicates data that needs to be backfilled
			zerolog.Ctx(ctx).Warn().
				Str("user_id", userID).
				Time("date", calendarDay).
				Msg("StreakCompensation row has empty MomentID - data may need backfilling")
		}

		res = append(res, internal.CalendarDayWithMoment{
			CalendarDay: calendarDay,
			MomentID:    momentIDStr,
		})

		return nil
	})
}

// streaksHaveSameLastPostDay compares if two streaks have the same last post calendar day
func streaksHaveSameLastPostDay(streak1, streak2 *pbUser.Streak) bool {
	// Both nil
	if streak1.LastPostCalendarDay == nil && streak2.LastPostCalendarDay == nil {
		return true
	}

	// One nil, one not nil
	if streak1.LastPostCalendarDay == nil || streak2.LastPostCalendarDay == nil {
		return false
	}

	// Both not nil, compare the dates
	return streak1.LastPostCalendarDay.Time().Equal(streak2.LastPostCalendarDay.Time())
}

// Gap represents a missing streak day and its associated moment ID
type Gap struct {
	Date     time.Time
	MomentID string
}

// RecoveryCalculation represents the result of a streak recovery calculation
type RecoveryCalculation struct {
	CurrentStreak   uint64
	EstimatedStreak uint64
	GapsToFill      []Gap
	IsEligible      bool
	ErrorMessage    string
}
