# includes:file://./../../../../config/templates/dev/grpc-api.yaml
# includes:file://./../../../../config/templates/dev/logging.yaml
# includes:file://./../../../../config/templates/dev/service-account.yaml
# includes:file://./../../../../config/templates/dev/datadog.yaml
# includes:file://./../../../../config/templates/dev/entity-user-spanner.yaml
# includes:file://./../../../../config/templates/dev/microservices.yaml
# includes:file://./../../../../config/templates/dev/safety-queue-publisher-sample-rates.yaml

application:
  technicalPort: 3001
  namespace: entity
  domain: entity
  feature: user
  name: entity-user-grpc-api
  type: api
  env: dev
  version: main

build-info:
  app: entity-user-grpc-api
  version: main
  branch: main
  commit: main

grpcApi: *grpcApi
logging: *logging
serviceAccount: *serviceAccount
datadog: *datadog

spanner: *entityUsersSpanner
basicInfoCache:
  namespace: "entity"
  labelSelector: "cluster=redis-default-standalone-a"
  circuitBreaker: false
  percentage: 20
realmojiCache:
  namespace: "entity"
  labelSelector: "cluster=redis-default-standalone-a"
  circuitBreaker: false

safetyPublisher:
  sampleRates: *safetyQueuePublisherSampleRates

momentClient:
  <<: *entityMoment
  lastNMoments: 50
  disableMomentRefresh: false

postClient:
  <<: *entityPost
  
OAWatcherInterval: "10s"
OAWatcherMaxConsecutivesErrors: 10
StreakCommitDelay: "500ms"

googlePlacesAPIConf:
  key: sm://backend-core-tagging-place-feature-api-key
  timeout: 2s