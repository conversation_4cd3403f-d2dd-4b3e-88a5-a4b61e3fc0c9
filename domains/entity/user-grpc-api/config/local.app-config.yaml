# includes:file://./../../../../config/templates/local/grpc-api.yaml
# includes:file://./../../../../config/templates/local/logging.yaml
# includes:file://./../../../../config/templates/local/profiler.yaml
# includes:file://./../../../../config/templates/local/service-account.yaml
# includes:file://./../../../../config/templates/local/datadog.yaml
# includes:file://./../../../../config/templates/local/spanner.yaml
# includes:file://./../../../../config/templates/local/safety-queue-publisher-sample-rates.yaml
# includes:file://./../../../../config/templates/local/microservices.yaml

application:
  technicalPort: 3001
  namespace: entity
  domain: entity
  feature: user
  name: entity-user-grpc-api
  type: api
  env: local
  version: local

build-info:
  app: entity-user-grpc-api
  version: local
  branch: main
  commit: local

serviceAccount: *serviceAccount
logging: *logging
datadog: *datadog
grpcApi:
  <<: *grpcApi
  port: *entityUserGrpcPort
  maxIncomingConnections: 1000

spanner: 
  <<: *spanner
  database: "projects/backend-core-dev/instances/bereal-local-instance/databases/entity-user"

safetyPublisher:
  sampleRates: *safetyQueuePublisherSampleRates

momentClient:
  <<: *entityMoment
  lastNMoments: 50
  disableMomentRefresh: false

postClient:
  <<: *entityPost

OAWatcherInterval: "10s"
OAWatcherMaxConsecutivesErrors: 10
StreakCommitDelay: "500ms"

googlePlacesAPIConf:
  key: google-places-api-key
  timeout: 2s