# includes:file://./../../../../config/templates/prod/grpc-api.yaml
# includes:file://./../../../../config/templates/prod/logging.yaml
# includes:file://./../../../../config/templates/prod/service-account.yaml
# includes:file://./../../../../config/templates/prod/datadog.yaml
# includes:file://./../../../../config/templates/prod/entity-user-spanner.yaml
# includes:file://./../../../../config/templates/prod/microservices.yaml
# includes:file://./../../../../config/templates/prod/safety-queue-publisher-sample-rates.yaml

application:
  technicalPort: 3001
  namespace: entity
  domain: entity
  feature: user
  name: entity-user-grpc-api
  type: api
  env: prod
  version: main

build-info:
  app: entity-user-grpc-api
  version: main
  branch: main
  commit: main

grpcApi:
  <<: *grpcApi
  sendDetailedErrors: true
logging: *logging
serviceAccount: *serviceAccount
datadog: *datadog

spanner: 
  <<: *entityUsersSpanner
  minOpened: 8_000
  maxOpened: 10_000
  maxIdle: 9_000
  gRPCConnectionPoolSize: 400

basicInfoCache:
  namespace: "entity"
  labelSelector: "app=redis-entity-user-standalone-a"
  circuitBreaker: false
  # Tested to 90% from the 2024-04-17 to 2024-05-03, spanner does not absorb it properly, so we revert to 100% cache usage
  percentage: 100
# realmojiCache:
#   namespace: "entity"
#   labelSelector: "app=redis-entity-user-standalone-a"
#   circuitBreaker: false

safetyPublisher:
 sampleRates: *safetyQueuePublisherSampleRates

momentClient:
  <<: *entityMoment
  lastNMoments: 50
  disableMomentRefresh: false

postClient:
  <<: *entityPost  

OAWatcherInterval: "10s"
OAWatcherMaxConsecutivesErrors: 10
StreakCommitDelay: "500ms"

googlePlacesAPIConf:
  key: sm://backend-core-tagging-place-feature-api-key
  timeout: 2s