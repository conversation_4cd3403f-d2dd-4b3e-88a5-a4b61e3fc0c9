# Code generated by ./domains/tooling/genconfig-go; DO NOT EDIT.
backend-go-apps:
  configFiles:
    config.yml: |
      # Code generated by ./domains/tooling/genconfig-go; DO NOT EDIT.
      
      #region included-conf
      # file: ./../../../../config/templates/prod/grpc-api.yaml
      tmpl-grpcApi: &grpcApi
        port: 8080
        grpcReflection: false
        host: 0.0.0.0
        maxConnectionAge: "30s"
        maxConnectionAgeGrace: "10s"
        signatureSecret: sm://backend-core-common-api-request-signature-secret
        signatureOnTime: false
        gracefulStopDuration: 10s
        TLSPort: 8081
        enableTLS: true
        withStats: false
      
      # file: ./../../../../config/templates/prod/logging.yaml
      tmpl-logging: &logging
        level: warn
      # file: ./../../../../config/templates/prod/service-account.yaml
      tmpl-serviceAccount: &serviceAccount
        projectID: "backend-core-prod"
      
      # file: ./../../../../config/templates/prod/datadog.yaml
      tmpl-datadog: &datadog
        enable: true
        debug: false
        sampling: 1e-4
      # file: ./../../../../config/templates/prod/entity-user-spanner.yaml
      tmpl-entityUsersSpanner: &entityUsersSpanner
        database: "projects/backend-core-prod/instances/entity-user/databases/entity-user"
        minOpened: 100
        maxOpened: 10_000
        maxIdle: 1_000
        trackSessionHandles: false
      
      # file: ./../../../../config/templates/prod/microservices.yaml
      tmpl-realtimeCore: &realtimeCore
        addr: "realtime-router-grpc-api-headless.realtime:8080"
      tmpl-toolingGatekeeper: &toolingGatekeeper
        addr: "dns:///tooling-gatekeeper-grpc-api-headless.tooling:8080"
        batchSize: 50
        batchTimeout: 100 # ms
      tmpl-toolingExperiment: &toolingExperiment
        addr: "dns:///tooling-experiment-grpc-api-headless.tooling:8080"
      tmpl-toolingPublisher: &toolingPublisher
        addr: "dns:///tooling-publisher-grpc-api-headless.tooling:8080"
      tmpl-entityUser: &entityUser
        addr: "dns:///entity-user-grpc-api-headless.entity.svc.cluster.local:8080"
      tmpl-entityCounter: &entityCounter
        addr: "dns:///entity-counter-grpc-api-headless.entity-counter.svc.cluster.local:8080"
      tmpl-entityActivity: &entityActivity
        addr: "dns:///entity-activity-grpc-api-headless.entity-activity.svc.cluster.local:8080"
      tmpl-entityUserCache: &entityUserCache
        addr: "dns:///entity-user-cache-grpc-api-headless.entity.svc.cluster.local:8080"
      tmpl-relationshipGraph: &relationshipGraph
        addr: "dns:///relationship-graph-grpc-api-headless.relationship-graph.svc.cluster.local:8080"
      tmpl-relationshipRanking: &relationshipRanking
        addr: "dns:///relationship-ranking-grpc-api-headless.relationship.svc.cluster.local:8080"
      tmpl-relationshipTag: &relationshipTag
        addr: "dns:///relationship-tag-grpc-api-headless.relationship-tag.svc.cluster.local:8080"
      tmpl-officialAccountsBackend: &officialAccountsBackend
        addr: "dns:///officialaccounts-backend-grpc-api-headless.officialaccounts.svc.cluster.local:8080"
      tmpl-entityPost: &entityPost
        addr: "dns:///entity-post-grpc-api-headless.entity-post.svc.cluster.local:8080"
      tmpl-entityMoment: &entityMoment
        addr: "dns:///entity-moment-grpc-api-headless.entity.svc.cluster.local:8080"
      tmpl-entityEvent: &entityEvent
        addr: "dns:///entity-event-grpc-api-headless.entity.svc.cluster.local:8080"
      tmpl-entityExplore: &entityExplore
        addr: "dns:///entity-explore-grpc-api-headless.entity-explore.svc.cluster.local:8080"
      tmpl-entityTopic: &entityTopic
        addr: "dns:///entity-topic-grpc-api-headless.entity-topic.svc.cluster.local:8080"
      tmpl-relationshipFriendRequest: &relationshipFriendRequest
        addr: "dns:///relationship-friendrequest-grpc-api-headless.bereal-prod.svc.cluster.local:8080"
      tmpl-searchOABackend: &searchOABackend
        addr: "dns:///search-oa-backend-grpc-api-headless.search.svc.cluster.local:8080"
      tmpl-searchBackend: &searchBackend
        addr: "dns:///search-backend-grpc-api-headless.search.svc.cluster.local:8080"
      tmpl-eventBackend: &eventBackend
        addr: "dns:///event-backend-grpc-api-headless.event.svc.cluster.local:8080"
      tmpl-notificationOverlord: &notificationOverlord
        addr: "dns:///notification-overlord-worker-headless.notification.svc.cluster.local:8080"
      tmpl-geoip: &geoip
        addr: "dns:///location-geoip-grpc-api-headless.location.svc.cluster.local:8080"
      tmpl-usersAtRisk: &usersAtRisk
        addr: "dns:///entity-users-at-risk-grpc-api-headless.entity.svc.cluster.local:8080"
      tmpl-feedMemories: &feedMemories
        addr: "dns:///feed-memories-grpc-api-headless.feed.svc.cluster.local:8080"
      tmpl-relationshipBackend: &relationshipBackend
        addr: "dns:///relationship-backend-grpc-api-headless.relationship-tag.svc.cluster.local:8080"
      
      # file: ./../../../../config/templates/prod/safety-queue-publisher-sample-rates.yaml
      tmpl-safety-queue-publisher-sample-rates: &safetyQueuePublisherSampleRates
        - key: conversation_message_created
          value: 0.0
        - key: post_created
          value: 0.0
        - key: profile_photo_created
          value: 1.0
        - key: best_post
          value: 1.0
      #endregion
      application:
        technicalPort: 3001
        namespace: entity
        domain: entity
        feature: user
        name: entity-user-grpc-api
        type: api
        env: prod
        version: main
      
      build-info:
        app: entity-user-grpc-api
        version: main
        branch: main
        commit: main
      
      grpcApi:
        <<: *grpcApi
        sendDetailedErrors: true
      logging: *logging
      serviceAccount: *serviceAccount
      datadog: *datadog
      
      spanner: 
        <<: *entityUsersSpanner
        minOpened: 8_000
        maxOpened: 10_000
        maxIdle: 9_000
        gRPCConnectionPoolSize: 400
      
      basicInfoCache:
        namespace: "entity"
        labelSelector: "app=redis-entity-user-standalone-a"
        circuitBreaker: false
        # Tested to 90% from the 2024-04-17 to 2024-05-03, spanner does not absorb it properly, so we revert to 100% cache usage
        percentage: 100
      # realmojiCache:
      #   namespace: "entity"
      #   labelSelector: "app=redis-entity-user-standalone-a"
      #   circuitBreaker: false
      
      safetyPublisher:
       sampleRates: *safetyQueuePublisherSampleRates
      
      momentClient:
        <<: *entityMoment
        lastNMoments: 50
        disableMomentRefresh: false
      
      postClient:
        <<: *entityPost  
      
      OAWatcherInterval: "10s"
      OAWatcherMaxConsecutivesErrors: 10
      StreakCommitDelay: "500ms"
      
      googlePlacesAPIConf:
        key: sm://backend-core-tagging-place-feature-api-key
        timeout: 2s
