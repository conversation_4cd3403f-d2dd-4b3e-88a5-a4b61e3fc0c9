// ▶️ user-grpc-api exposes CRUD user entity methods
package main

import (
	"context"
	"net"
	"time"

	"github.com/BeReal-App/backend-go/shared/3p/googleplacesnew"

	"github.com/BeReal-App/backend-go/domains/entity/place"
	post_client "github.com/BeReal-App/backend-go/domains/entity/post-client"

	gPS "cloud.google.com/go/pubsub"
	"github.com/BeReal-App/backend-go/domains/entity/moment"
	user_cache "github.com/BeReal-App/backend-go/domains/entity/user-cache"
	"github.com/BeReal-App/backend-go/domains/entity/user-grpc-api/internal"
	safety "github.com/BeReal-App/backend-go/domains/safety/queue-publisher"
	"github.com/BeReal-App/backend-go/shared/config"
	"github.com/BeReal-App/backend-go/shared/consistent/pool"
	"github.com/BeReal-App/backend-go/shared/grpc"
	"github.com/BeReal-App/backend-go/shared/k8s"
	"github.com/BeReal-App/backend-go/shared/redis"
	"github.com/BeReal-App/backend-go/shared/spanner"
	"github.com/BeReal-App/backend-go/shared/technical"
	"github.com/rs/zerolog"
	"k8s.io/client-go/kubernetes"

	pbUser "github.com/BeReal-App/backend-go/proto/entity/user/v1"
	gGrpc "google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/keepalive"
)

func main() {
	cfg := Config{}
	ctx, l := config.Bootstrap(context.Background(), "entity-user-grpc-api", &cfg)

	// spanner client
	spannerClient, err := spanner.NewSpannerClient(ctx, l, cfg.Spanner, *cfg.Application.Env)
	if err != nil {
		l.Fatal().Err(err).Msg("cannot use spanner")
	}
	defer spannerClient.Close()

	// pubsub client
	pubsubClient, err := gPS.NewClientWithConfig(
		ctx,
		*cfg.ServiceAccount.ProjectID,
		&gPS.ClientConfig{
			EnableOpenTelemetryTracing: true,
		},
	)
	if err != nil {
		l.Fatal().Err(err).Msg("cannot use pubsub")
	}
	defer func() {
		if err := pubsubClient.Close(); err != nil {
			l.Err(err).Msg("error while closing pubsub client")
		}
	}()

	// pubsub topic
	// TODO: eventually share it in shared/pubsub
	topicName := "user-written"
	topic := pubsubClient.Topic(topicName)
	topic.PublishSettings = gPS.DefaultPublishSettings
	topic.PublishSettings.DelayThreshold = time.Millisecond * 10
	topic.PublishSettings.CountThreshold = 10
	exists, err := topic.Exists(ctx)
	if err != nil {
		l.Fatal().Err(err).Msgf("error while checking topic %s exists in GCP", topicName)
	}
	if !exists {
		l.Fatal().Msgf("topic %s does not exists", topicName)
	}
	defer topic.Stop()

	// basic info cache with its redis client
	var basicInfoCache user_cache.BasicInfo = &user_cache.BasicInfoNoop{}
	var realmojisCache user_cache.Realmojis = &user_cache.RealmojisNoop{}
	if cfg.BasicInfoCache != nil || cfg.RealmojiCache != nil {
		kubeClient, err := k8s.NewKubeClient()
		if err != nil {
			l.Fatal().Err(err).Msg("error while instantiating k8s client")
		}

		// basic info
		if cfg.BasicInfoCache != nil {
			pool := redisPool(ctx, *l, kubeClient, *cfg.Application.Name, *cfg.BasicInfoCache.Namespace, *cfg.BasicInfoCache.LabelSelector)
			defer func() {
				if err := pool.Close(); err != nil {
					l.Error().Err(err).Msgf("error while closing basicInfo redis pool")
				}
			}()

			woCB := user_cache.NewBasicInfoRedisPool(*l, pool, cfg.BasicInfoCache.Percentage)
			defer woCB.Close()
			basicInfoCache = woCB
			if *cfg.BasicInfoCache.CircuitBreaker {
				withCB, err := woCB.CircuitBreaker()
				if err != nil {
					basicInfoCache = woCB
					l.Error().Err(err).Msgf("error while instantiating basic info cache with its CircuitBreaker, fallbacking to the version without CB: %s", err.Error())
				} else {
					basicInfoCache = withCB
				}
			}
		}

		if cfg.RealmojiCache != nil {
			pool := redisPool(ctx, *l, kubeClient, *cfg.Application.Name, *cfg.BasicInfoCache.Namespace, *cfg.BasicInfoCache.LabelSelector)
			defer func() {
				if err := pool.Close(); err != nil {
					l.Error().Err(err).Msgf("error while closing basicInfo redis pool")
				}
			}()

			realmojisCache = user_cache.NewRealmojisRedisPool(*l, pool)
			defer realmojisCache.Close()
		}
	}

	// safety publisher
	var safetyQueuePublisher *safety.Publisher
	if cfg.SafetyPublisher != nil {
		sp, err := safety.NewPublisher(ctx, cfg.ServiceAccount, l, *cfg.SafetyPublisher)
		if err != nil {
			l.Err(err).Msg("got error while trying to init safetyQueuePublisher")
		} else {
			safetyQueuePublisher = sp
		}
	}

	// moment client
	momentsClient, err := moment.ProvideClient(ctx, cfg.MomentClient, *l, config.CallerID(*cfg.Application.Name))
	if err != nil {
		l.Fatal().Err(err).Msg("error while instantiating moment client")
	}
	defer momentsClient.Close()

	// post client
	postClient, closePostClient, err := post_client.New(*cfg.PostClient.Addr, *cfg.Application.Name)
	if err != nil {
		l.Fatal().Err(err).Msg("error while instantiating post client")
	}
	defer func() {
		if err := closePostClient(); err != nil {
			l.Error().Err(err).Msg("error while closing post client")
		}
	}()

	// server
	server, err := internal.NewServer(
		topic,
		spannerClient,
		basicInfoCache,
		realmojisCache,
		safetyQueuePublisher,
		&cfg.StreakCommitDelay,
		momentsClient,
		place.NewSpannerService(spannerClient, googleplacesnew.NewClient(cfg.GooglePlacesAPIConf)),
		postClient,
	)
	if err != nil {
		l.Fatal().Err(err).Msg("error while instantiating server")
	}

	// starting the OA user watcher
	l.Info().Msg("starting the OA user goroutine watcher")
	if err := server.RefreshOAUsers(ctx, l); err != nil {
		l.Fatal().Err(err).Msg("error while initializing OA in memory cache")
	}
	wCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	go server.WatchOAUsers(wCtx, l, cfg.OAWatcherInterval, *cfg.OAWatcherMaxConsecutivesErrors, func() {
		technical.SetUnhealthy()
	})

	// API
	grpc.BootstrapGRPCApi(l, &grpc.BootstrapGRPCApiOpts{
		Application: cfg.Application,
		BuildInfo:   cfg.BuildInfo,
		Profiler:    cfg.Profiler,
		Datadog:     cfg.Datadog,
		GrpcAPI:     cfg.GrpcAPI,
		LogInterceptorOpts: &grpc.LogInterceptorOpts{
			ErrCodesLevel: map[codes.Code]zerolog.Level{
				codes.NotFound: zerolog.Disabled,
			},
		},
		DisableAuth:        true,
		DisableUserIDCheck: true,
		GetGrpcServerOpts: func() []gGrpc.ServerOption {
			return []gGrpc.ServerOption{
				// https://medium.com/jamf-engineering/how-three-lines-of-configuration-solved-our-grpc-scaling-issues-in-kubernetes-ca1ff13f7f06
				gGrpc.KeepaliveParams(keepalive.ServerParameters{
					MaxConnectionAge:      *cfg.GrpcAPI.MaxConnectionAge,
					MaxConnectionAgeGrace: *cfg.GrpcAPI.MaxConnectionAgeGrace,
				}),
			}
		},
		RegisterServers: func(srv *gGrpc.Server) {
			pbUser.RegisterUserServiceServer(srv, server)
		},
	})
}

func redisPool(ctx context.Context, l zerolog.Logger, kubeClient *kubernetes.Clientset, appName string, namespace string, labelSelector string) *pool.Base[*pool.RedisMember] {
	pool, err := pool.NewRedis(
		ctx,
		l,
		&pool.Kubernetes{
			Client:        kubeClient,
			Namespace:     namespace,
			LabelSelector: labelSelector,
		}, &redis.ClientOpts{
			Name: appName,
			// have lesser foot print since we will have a single co per redis pod now
			RingScaleEachConn: 8,
			// have lesser foot print since we will have a single co per redis pod now
			PipelineMultiplex: -1,
			InitRetry:         3,
			DisableRetry:      true,
			// dividing by 10 the defaults
			Dialer: net.Dialer{
				Timeout:   time.Millisecond * 500,
				KeepAlive: time.Millisecond * 100,
			},
		})
	if err != nil {
		l.Fatal().Err(err).Msg("error while instantiating redis clients pool")
	}
	return pool
}
