package main

import (
	"time"

	"github.com/BeReal-App/backend-go/shared/3p/googleplacesnew"

	"github.com/BeReal-App/backend-go/shared/config"
	"github.com/BeReal-App/backend-go/shared/spanner"

	"github.com/BeReal-App/backend-go/domains/entity/moment"
	post_client "github.com/BeReal-App/backend-go/domains/entity/post-client"
	safety "github.com/BeReal-App/backend-go/domains/safety/queue-publisher"
)

type CacheConfig struct {
	Namespace     *string `validate:"required"`
	LabelSelector *string `validate:"required"`

	// set CacheCircuitBreaker to `true` to enable circuit breaker on top of all caches nodes
	CircuitBreaker *bool `validate:"required"`

	// how many user should use this cache
	Percentage uint64 // 100 means 100%
}

type Config struct {
	config.BaseConfig

	GrpcAPI *config.GrpcAPI `validate:"required"`
	Spanner *spanner.Config `validate:"required"`

	BasicInfoCache *CacheConfig // default to nil = disabled
	RealmojiCache  *CacheConfig // default to nil = disabled

	SafetyPublisher *safety.PublisherConfig

	MomentClient *moment.ClientConfig `validate:"required"`
	PostClient   *post_client.Config  `validate:"required"`

	OAWatcherInterval              time.Duration `validate:"required"`
	OAWatcherMaxConsecutivesErrors *int          `validate:"required"`
	StreakCommitDelay              time.Duration `validate:"required"`

	GooglePlacesAPIConf googleplacesnew.Config `validate:"required"`
}
