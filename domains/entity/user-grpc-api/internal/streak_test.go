package internal

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"testing"
	"time"

	"cloud.google.com/go/civil"
	gS "cloud.google.com/go/spanner"
	"github.com/BeReal-App/backend-go/domains/entity"
	"github.com/BeReal-App/backend-go/domains/entity/moment-grpc-api/models"
	pbCommon "github.com/BeReal-App/backend-go/proto/common/core/v1"
	pbUser "github.com/BeReal-App/backend-go/proto/entity/user/v1"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// Mock SpannerClient for testing
type MockSpannerClient struct {
	mock.Mock
}

func (m *MockSpannerClient) Single() *MockReadonlyTransaction {
	args := m.Called()
	return args.Get(0).(*MockReadonlyTransaction)
}

func (m *MockSpannerClient) ReadWriteTransactionWithOptions(ctx context.Context, f func(context.Context, *gS.ReadWriteTransaction) error, opts gS.TransactionOptions) (gS.CommitResponse, error) {
	args := m.Called(ctx, mock.Anything, opts)
	// Execute the function with a nil transaction - we're just testing it gets called
	_ = f(ctx, nil)
	return args.Get(0).(gS.CommitResponse), args.Error(1)
}

// Mock ReadonlyTransaction for testing
type MockReadonlyTransaction struct {
	mock.Mock
}

func (m *MockReadonlyTransaction) ReadRow(ctx context.Context, table string, key gS.Key, columns []string) (*gS.Row, error) {
	args := m.Called(ctx, table, key, columns)
	return args.Get(0).(*gS.Row), args.Error(1)
}

func (m *MockReadonlyTransaction) Query(ctx context.Context, statement gS.Statement) *MockRowIterator {
	args := m.Called(ctx, statement)
	return args.Get(0).(*MockRowIterator)
}

// Mock RowIterator for testing
type MockRowIterator struct {
	mock.Mock
}

func (m *MockRowIterator) Do(f func(*gS.Row) error) error {
	args := m.Called(mock.Anything)
	return args.Error(0)
}

func (m *MockRowIterator) Stop() {}

// MockMomentsClient is a mock for the moments client
type MockMomentsClient struct {
	Moments []models.Moment
}

func (m *MockMomentsClient) GetCurrentAndPreviousByRegion() []models.Moment {
	return m.Moments
}

// MockRow creates a mock Spanner row for testing
func MockRow(values map[string]any) *gS.Row {
	return &gS.Row{}
}

// TestServer embeds the Server struct to allow adding mock methods
type TestServer struct {
	Server
}

func TestIsStreakOutdated(t *testing.T) {
	// Create mock current and previous moments slice
	currentAndPreviousMoments := []models.Moment{
		{ID: "current-moment-1"},
		{ID: "previous-moment-2"},
		{ID: "current-moment-123"},
	}

	tests := []struct {
		name             string
		lastPostMomentID string
		moments          []models.Moment
		expected         bool
	}{
		{
			name:             "moment exists in current/previous moments - should not be outdated",
			lastPostMomentID: "current-moment-1",
			moments:          currentAndPreviousMoments,
			expected:         false, // not outdated because it exists in current/previous moments
		},
		{
			name:             "moment does not exist in current/previous moments - should be outdated",
			lastPostMomentID: "old-moment-456",
			moments:          currentAndPreviousMoments,
			expected:         true, // outdated because it doesn't exist in current/previous moments
		},
		{
			name:             "another current moment - should not be outdated",
			lastPostMomentID: "current-moment-123",
			moments:          currentAndPreviousMoments,
			expected:         false, // not outdated because it exists in current/previous moments
		},
		{
			name:             "nil moments - should not be outdated",
			lastPostMomentID: "any-moment-id",
			moments:          nil,
			expected:         false, // not outdated because we don't have moments data to verify against
		},
		{
			name:             "empty moments - should be outdated",
			lastPostMomentID: "any-moment-id",
			moments:          []models.Moment{},
			expected:         true, // outdated because moments is empty
		},
		{
			name:             "whitespace only lastPostMomentID - should be outdated",
			lastPostMomentID: "   ",
			moments:          currentAndPreviousMoments,
			expected:         true, // outdated because trimmed ID doesn't exist
		},
		{
			name:             "case sensitive moment ID check",
			lastPostMomentID: "Current-Moment-1", // different case
			moments:          currentAndPreviousMoments,
			expected:         true, // outdated because case doesn't match
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isStreakOutdated(context.Background(), tt.lastPostMomentID, tt.moments)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCorrectStreak(t *testing.T) {
	// Create mock current and previous moments slice
	currentAndPreviousMoments := []models.Moment{
		{ID: "current-moment-1"},
		{ID: "previous-moment-2"},
	}

	tests := []struct {
		name        string
		streak      entity.UserStreak
		moments     []models.Moment
		shouldReset bool
	}{
		{
			name: "streak with moment in current/previous moments - should not reset",
			streak: entity.UserStreak{
				Length:              5,
				LastPostMomentID:    gS.NullString{StringVal: "current-moment-1", Valid: true},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     currentAndPreviousMoments,
			shouldReset: false,
		},
		{
			name: "streak with moment not in current/previous moments - should reset",
			streak: entity.UserStreak{
				Length:              5,
				LastPostMomentID:    gS.NullString{StringVal: "old-moment-123", Valid: true},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     currentAndPreviousMoments,
			shouldReset: true,
		},
		{
			name: "streak with invalid moment ID - should not reset",
			streak: entity.UserStreak{
				Length:              5,
				LastPostMomentID:    gS.NullString{Valid: false},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     currentAndPreviousMoments,
			shouldReset: false,
		},
		{
			name: "streak with zero length - should not reset",
			streak: entity.UserStreak{
				Length:              0,
				LastPostMomentID:    gS.NullString{StringVal: "old-moment-123", Valid: true},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     currentAndPreviousMoments,
			shouldReset: false,
		},
		{
			name: "streak with nil moments - should not reset",
			streak: entity.UserStreak{
				Length:              5,
				LastPostMomentID:    gS.NullString{StringVal: "any-moment-id", Valid: true},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     nil,
			shouldReset: false,
		},
		{
			name: "streak with empty moments - should reset",
			streak: entity.UserStreak{
				Length:              10,
				LastPostMomentID:    gS.NullString{StringVal: "any-moment-id", Valid: true},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     []models.Moment{},
			shouldReset: true,
		},
		{
			name: "negative streak length - should not reset",
			streak: entity.UserStreak{
				Length:              -1,
				LastPostMomentID:    gS.NullString{StringVal: "old-moment-123", Valid: true},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     currentAndPreviousMoments,
			shouldReset: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			correctedStreak := correctStreak(context.Background(), tt.streak, tt.moments)
			if tt.shouldReset {
				require.Equal(t, uint64(0), correctedStreak.Length)
				require.Nil(t, correctedStreak.LastPostCalendarDay)
			} else {
				require.Equal(t, uint64(tt.streak.Length), correctedStreak.Length)
			}
		})
	}
}

// Test to verify that modifying the input streak doesn't affect the original
func TestCorrectStreakDoesNotModifyOriginal(t *testing.T) {
	originalStreak := entity.UserStreak{
		UserID:              "test-user",
		Length:              5,
		LastPostMomentID:    gS.NullString{StringVal: "old-moment-123", Valid: true},
		LastPostCalendarDay: gS.NullDate{Valid: true},
	}

	// Create a copy to compare later
	streakBeforeCall := originalStreak

	// Call correctStreak with moments that should cause a reset
	moments := []models.Moment{
		{ID: "current-moment-1"},
		{ID: "current-moment-2"},
	}

	correctedStreak := correctStreak(context.Background(), originalStreak, moments)

	// Verify the corrected streak is reset
	require.Equal(t, uint64(0), correctedStreak.Length)

	// Verify the original streak is unchanged
	require.Equal(t, streakBeforeCall.Length, originalStreak.Length)
	require.Equal(t, streakBeforeCall.LastPostMomentID, originalStreak.LastPostMomentID)
	require.Equal(t, streakBeforeCall.LastPostCalendarDay, originalStreak.LastPostCalendarDay)
}

// Test edge cases for moment ID matching
func TestMomentIDMatching(t *testing.T) {
	tests := []struct {
		name       string
		momentID   string
		moments    []models.Moment
		shouldFind bool
	}{
		{
			name:       "exact match",
			momentID:   "moment-123",
			moments:    []models.Moment{{ID: "moment-123"}},
			shouldFind: true,
		},
		{
			name:       "no match - different ID",
			momentID:   "moment-456",
			moments:    []models.Moment{{ID: "moment-123"}},
			shouldFind: false,
		},
		{
			name:       "case sensitive - different case",
			momentID:   "Moment-123",
			moments:    []models.Moment{{ID: "moment-123"}},
			shouldFind: false,
		},
		{
			name:       "moment with empty ID in slice",
			momentID:   "moment-123",
			moments:    []models.Moment{{ID: ""}},
			shouldFind: false,
		},
		{
			name:       "multiple moments - first match",
			momentID:   "moment-123",
			moments:    []models.Moment{{ID: "moment-123"}, {ID: "moment-456"}},
			shouldFind: true,
		},
		{
			name:       "multiple moments - last match",
			momentID:   "moment-456",
			moments:    []models.Moment{{ID: "moment-123"}, {ID: "moment-456"}},
			shouldFind: true,
		},
		{
			name:       "unicode characters",
			momentID:   "moment-123-🚀",
			moments:    []models.Moment{{ID: "moment-123-🚀"}},
			shouldFind: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isOutdated := isStreakOutdated(context.Background(), tt.momentID, tt.moments)
			expected := !tt.shouldFind // isStreakOutdated returns true when moment is NOT found
			require.Equal(t, expected, isOutdated)
		})
	}
}

// Test context behavior
func TestContextHandling(t *testing.T) {
	// Test with cancelled context
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // Cancel immediately

	moments := []models.Moment{{ID: "test-moment"}}

	// Function should still work even with cancelled context
	result := isStreakOutdated(ctx, "non-existent-moment", moments)
	require.True(t, result)
}

// Benchmark tests
func BenchmarkIsStreakOutdated(b *testing.B) {
	moments := make([]models.Moment, 100)
	for i := range 100 {
		moments[i] = models.Moment{ID: fmt.Sprintf("moment-%d", i)}
	}

	ctx := context.Background()

	b.ResetTimer()
	for b.Loop() {
		isStreakOutdated(ctx, "moment-50", moments)
	}
}

func BenchmarkCorrectStreak(b *testing.B) {
	moments := []models.Moment{
		{ID: "current-moment-1"},
		{ID: "previous-moment-2"},
	}

	streak := entity.UserStreak{
		Length:              5,
		LastPostMomentID:    gS.NullString{StringVal: "old-moment-123", Valid: true},
		LastPostCalendarDay: gS.NullDate{Valid: true},
	}

	ctx := context.Background()

	b.ResetTimer()
	for b.Loop() {
		correctStreak(ctx, streak, moments)
	}
}

// Test concurrent access to verify race condition behavior
func TestCorrectStreakConcurrentAccess(t *testing.T) {
	const numGoroutines = 100
	const numIterations = 10

	originalStreak := entity.UserStreak{
		UserID:              "test-user",
		Length:              5,
		LastPostMomentID:    gS.NullString{StringVal: "old-moment-123", Valid: true},
		LastPostCalendarDay: gS.NullDate{Valid: true},
	}

	moments := []models.Moment{
		{ID: "current-moment-1"},
		{ID: "current-moment-2"},
	}

	// Run many goroutines concurrently to try to expose race conditions
	var wg sync.WaitGroup
	ctx := context.Background()

	for range numGoroutines {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for range numIterations {
				correctedStreak := correctStreak(ctx, originalStreak, moments)
				// All should be reset since moment doesn't exist in moments slice
				require.Equal(t, uint64(0), correctedStreak.Length)
			}
		}()
	}

	wg.Wait()

	// Verify original streak is still unchanged after all concurrent calls
	require.Equal(t, int64(5), originalStreak.Length)
	require.Equal(t, "old-moment-123", originalStreak.LastPostMomentID.StringVal)
}

// Test large numbers of moments for performance
func TestIsStreakOutdatedLargeDataset(t *testing.T) {
	// Create a large slice of moments
	moments := make([]models.Moment, 10000)
	for i := range 10000 {
		moments[i] = models.Moment{ID: fmt.Sprintf("moment-%d", i)}
	}

	ctx := context.Background()

	// Test finding a moment at the beginning
	result := isStreakOutdated(ctx, "moment-0", moments)
	require.False(t, result)

	// Test finding a moment in the middle
	result = isStreakOutdated(ctx, "moment-5000", moments)
	require.False(t, result)

	// Test finding a moment at the end
	result = isStreakOutdated(ctx, "moment-9999", moments)
	require.False(t, result)

	// Test not finding a moment
	result = isStreakOutdated(ctx, "moment-10000", moments)
	require.True(t, result)
}

// TestSortGapsByDate tests the sortGapsByDate function
func TestSortGapsByDate(t *testing.T) {
	// Create unsorted gaps
	gaps := []*pbUser.StreakGap{
		{
			Date: &pbCommon.Date{
				Year:  2023,
				Month: 6,
				Day:   15,
			},
			MomentId: "moment-3",
		},
		{
			Date: &pbCommon.Date{
				Year:  2023,
				Month: 1,
				Day:   10,
			},
			MomentId: "moment-1",
		},
		{
			Date: &pbCommon.Date{
				Year:  2023,
				Month: 3,
				Day:   20,
			},
			MomentId: "moment-2",
		},
	}

	// Sort gaps
	sortGapsByDate(gaps)

	// Verify sort order (oldest first)
	require.Equal(t, int32(1), gaps[0].Date.Month) // January (month 1) should be first
	require.Equal(t, int32(3), gaps[1].Date.Month) // March (month 3) should be second
	require.Equal(t, int32(6), gaps[2].Date.Month) // June (month 6) should be last
}

// TestCalculateGapsFromPosts tests the calculateGapsFromPosts function
func TestCalculateGapsFromPosts(t *testing.T) {
	ctx := context.Background()
	server := &Server{}

	// Test data
	userID := "test-user-123"
	now := time.Now()
	lookbackDays := int64(7)
	lastPostDate := civil.Date{}

	// User posts (days with posts)
	userPosts := map[string]bool{
		now.AddDate(0, 0, -3).Format("2006-01-02"): true, // Posted 3 days ago
		now.AddDate(0, 0, -5).Format("2006-01-02"): true, // Posted 5 days ago
	}

	// Available moments - need to use FiredAt instead of LocalDate
	yesterday := now.AddDate(0, 0, -1)
	twoDaysAgo := now.AddDate(0, 0, -2)
	threeDaysAgo := now.AddDate(0, 0, -3)
	fourDaysAgo := now.AddDate(0, 0, -4)
	fiveDaysAgo := now.AddDate(0, 0, -5)
	sixDaysAgo := now.AddDate(0, 0, -6)

	moments := []models.Moment{
		{
			ID:      "moment-1",
			FiredAt: &yesterday,
		},
		{
			ID:      "moment-2",
			FiredAt: &twoDaysAgo,
		},
		{
			ID:      "moment-3",
			FiredAt: &threeDaysAgo,
		},
		{
			ID:      "moment-4",
			FiredAt: &fourDaysAgo,
		},
		{
			ID:      "moment-5",
			FiredAt: &fiveDaysAgo,
		},
		{
			ID:      "moment-6",
			FiredAt: &sixDaysAgo,
		},
	}

	// Call the function
	gaps, err := server.calculateGapsFromPosts(ctx, userID, userPosts, moments, lookbackDays, lastPostDate, "UTC")

	// Assertions
	require.NoError(t, err)
	require.Equal(t, 4, len(gaps)) // Should find 4 gaps (days 1, 2, 4, 6)

	// Convert gaps to a map of dates for easier verification
	gapDates := make(map[string]bool)
	for _, gap := range gaps {
		dateStr := fmt.Sprintf("%04d-%02d-%02d", gap.Date.Year, gap.Date.Month, gap.Date.Day)
		gapDates[dateStr] = true
	}

	// Check for specific expected gaps
	yesterdayStr := now.AddDate(0, 0, -1).Format("2006-01-02")
	twoDaysAgoStr := now.AddDate(0, 0, -2).Format("2006-01-02")
	fourDaysAgoStr := now.AddDate(0, 0, -4).Format("2006-01-02")
	sixDaysAgoStr := now.AddDate(0, 0, -6).Format("2006-01-02")

	require.True(t, gapDates[yesterdayStr], "Should have gap for yesterday")
	require.True(t, gapDates[twoDaysAgoStr], "Should have gap for 2 days ago")
	require.True(t, gapDates[fourDaysAgoStr], "Should have gap for 4 days ago")
	require.True(t, gapDates[sixDaysAgoStr], "Should have gap for 6 days ago")
}

// TestCalculateStreakRecoveryLogic tests the core logic without mocking the server
func TestCalculateStreakRecoveryLogic(t *testing.T) {
	tests := []struct {
		name              string
		description       string
		userPosts         []UserPost
		moments           []models.Moment
		expectedGapsCount int
		expectedEstimated uint64
	}{
		{
			name:        "User with no gaps - not eligible",
			description: "User posted for all recent moments",
			userPosts: []UserPost{
				{PostID: "post1", MomentID: gS.NullString{StringVal: "moment-1", Valid: true}},
				{PostID: "post2", MomentID: gS.NullString{StringVal: "moment-2", Valid: true}},
				{PostID: "post3", MomentID: gS.NullString{StringVal: "moment-3", Valid: true}},
			},
			moments:           createTestMomentsForDays([]int{-1, -2, -3}),
			expectedGapsCount: 0,
			expectedEstimated: 3,
		},
		{
			name:        "User with one gap in last 30 days",
			description: "User missing one recent post",
			userPosts: []UserPost{
				{PostID: "post1", MomentID: gS.NullString{StringVal: "moment-1", Valid: true}},
				{PostID: "post3", MomentID: gS.NullString{StringVal: "moment-3", Valid: true}},
				// Missing moment-2 (gap)
			},
			moments:           createTestMomentsForDays([]int{-1, -2, -3}),
			expectedGapsCount: 1,
			expectedEstimated: 3,
		},
		{
			name:        "User with old break point",
			description: "Streak stops at old break, gaps found for missing posts",
			userPosts: []UserPost{
				{PostID: "post1", MomentID: gS.NullString{StringVal: "moment-1", Valid: true}},
				{PostID: "post3", MomentID: gS.NullString{StringVal: "moment-3", Valid: true}},
				{PostID: "post4", MomentID: gS.NullString{StringVal: "moment-4", Valid: true}},
				{PostID: "post5", MomentID: gS.NullString{StringVal: "moment-5", Valid: true}},
				{PostID: "post6", MomentID: gS.NullString{StringVal: "moment-6", Valid: true}},
				// Missing moment-2 and moment-7 (gaps in last 30 days since all are recent)
			},
			moments:           createTestMomentsForDays([]int{-1, -2, -3, -4, -5, -6, -7}),
			expectedGapsCount: 2, // moment-2 and moment-7 missing
			expectedEstimated: 7, // All moments filled, full streak length
		},
		{
			name:        "User with multiple gaps",
			description: "Multiple missing posts in recent period",
			userPosts: []UserPost{
				{PostID: "post1", MomentID: gS.NullString{StringVal: "moment-1", Valid: true}},
				// Missing moment-2 and moment-3 (gaps)
				{PostID: "post4", MomentID: gS.NullString{StringVal: "moment-4", Valid: true}},
				{PostID: "post5", MomentID: gS.NullString{StringVal: "moment-5", Valid: true}},
			},
			moments:           createTestMomentsForDays([]int{-1, -2, -3, -4, -5}),
			expectedGapsCount: 2,
			expectedEstimated: 5,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := &Server{}

			// Create post lookup map
			postsByMomentID := make(map[string]bool)
			for _, post := range tt.userPosts {
				if post.MomentID.Valid {
					postsByMomentID[post.MomentID.StringVal] = true
				}
			}

			// Find gaps in last 30 days
			now := time.Now()
			thirtyDaysAgo := now.AddDate(0, 0, -30)
			var gapsToFill []*pbUser.StreakGap

			for _, moment := range tt.moments {
				if moment.FiredAt.Before(thirtyDaysAgo) {
					continue
				}

				if !postsByMomentID[moment.ID] {
					gapsToFill = append(gapsToFill, &pbUser.StreakGap{
						MomentId: moment.ID,
					})
				}
			}

			// Calculate estimated streak
			estimatedStreak := server.calculateEstimatedStreak(context.Background(), "test-user", tt.moments, postsByMomentID, gapsToFill)

			// Verify results
			require.Equal(t, tt.expectedGapsCount, len(gapsToFill), "Gap count mismatch for: %s", tt.description)
			require.Equal(t, tt.expectedEstimated, estimatedStreak, "Estimated streak mismatch for: %s", tt.description)
		})
	}
}

// TestCalculateEstimatedStreakScenarios tests various streak calculation scenarios
func TestCalculateEstimatedStreakScenarios(t *testing.T) {
	server := &Server{}

	tests := []struct {
		name           string
		moments        []models.Moment
		posts          map[string]bool
		gaps           []*pbUser.StreakGap
		expectedStreak uint64
	}{
		{
			name:    "Perfect streak with no gaps",
			moments: createTestMomentsForDays([]int{-1, -2, -3, -4, -5}),
			posts: map[string]bool{
				"moment-1": true,
				"moment-2": true,
				"moment-3": true,
				"moment-4": true,
				"moment-5": true,
			},
			gaps:           []*pbUser.StreakGap{},
			expectedStreak: 5,
		},
		{
			name:    "Streak with filled gaps",
			moments: createTestMomentsForDays([]int{-1, -2, -3, -4, -5}),
			posts: map[string]bool{
				"moment-1": true,
				// moment-2 missing but will be filled
				"moment-3": true,
				"moment-4": true,
				"moment-5": true,
			},
			gaps:           []*pbUser.StreakGap{{MomentId: "moment-2"}},
			expectedStreak: 5,
		},
		{
			name:    "Streak broken by unfilled gap",
			moments: createTestMomentsForDays([]int{-1, -2, -3, -4, -5}),
			posts: map[string]bool{
				"moment-1": true,
				"moment-2": true,
				// moment-3 missing and NOT filled
				"moment-4": true,
				"moment-5": true,
			},
			gaps:           []*pbUser.StreakGap{}, // No gaps to fill
			expectedStreak: 2,                     // Stops at moment-3
		},
		{
			name:           "Empty streak",
			moments:        createTestMomentsForDays([]int{-1, -2, -3}),
			posts:          map[string]bool{},
			gaps:           []*pbUser.StreakGap{},
			expectedStreak: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := server.calculateEstimatedStreak(context.Background(), "test-user", tt.moments, tt.posts, tt.gaps)
			require.Equal(t, tt.expectedStreak, result)
		})
	}
}

// Helper functions for testing

// createTestMomentsForDays creates test moments for given days (negative numbers for days ago)
func createTestMomentsForDays(daysAgo []int) []models.Moment {
	var moments []models.Moment
	now := time.Now()

	for i, day := range daysAgo {
		momentTime := now.AddDate(0, 0, day)
		moments = append(moments, models.Moment{
			ID:      fmt.Sprintf("moment-%d", i+1),
			FiredAt: &momentTime,
		})
	}

	// Sort by FiredAt desc (most recent first) as expected by the function
	sort.Slice(moments, func(i, j int) bool {
		return moments[i].FiredAt.After(*moments[j].FiredAt)
	})

	return moments
}

// TestGapIdentificationScenarios tests gap identification edge cases
func TestGapIdentificationScenarios(t *testing.T) {
	tests := []struct {
		name              string
		posts             []UserPost
		momentsInLast30   int // Number of moments in last 30 days
		expectedGapsCount int
	}{
		{
			name: "All recent moments covered",
			posts: []UserPost{
				{PostID: "post1", MomentID: gS.NullString{StringVal: "moment-1", Valid: true}},
				{PostID: "post2", MomentID: gS.NullString{StringVal: "moment-2", Valid: true}},
				{PostID: "post3", MomentID: gS.NullString{StringVal: "moment-3", Valid: true}},
			},
			momentsInLast30:   3,
			expectedGapsCount: 0,
		},
		{
			name: "Some recent moments missing",
			posts: []UserPost{
				{PostID: "post1", MomentID: gS.NullString{StringVal: "moment-1", Valid: true}},
				// Missing moment-2 and moment-3
			},
			momentsInLast30:   3,
			expectedGapsCount: 2,
		},
		{
			name: "Posts with invalid moment IDs",
			posts: []UserPost{
				{PostID: "post1", MomentID: gS.NullString{StringVal: "moment-1", Valid: true}},
				{PostID: "post2", MomentID: gS.NullString{Valid: false}}, // Invalid moment ID
			},
			momentsInLast30:   3,
			expectedGapsCount: 2, // moment-2 and moment-3 missing
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create moments for last 30 days
			moments := createTestMomentsForDays(createDayRange(-1, -tt.momentsInLast30))

			// Create post lookup map
			postsByMomentID := make(map[string]bool)
			for _, post := range tt.posts {
				if post.MomentID.Valid {
					postsByMomentID[post.MomentID.StringVal] = true
				}
			}

			// Count gaps
			gapCount := 0
			for _, moment := range moments {
				if !postsByMomentID[moment.ID] {
					gapCount++
				}
			}

			require.Equal(t, tt.expectedGapsCount, gapCount)
		})
	}
}

// createDayRange creates a slice of day offsets (e.g., [-1, -2, -3])
func createDayRange(start, end int) []int {
	var days []int
	if start > end {
		for i := start; i >= end; i-- {
			days = append(days, i)
		}
	} else {
		for i := start; i <= end; i++ {
			days = append(days, i)
		}
	}
	return days
}
