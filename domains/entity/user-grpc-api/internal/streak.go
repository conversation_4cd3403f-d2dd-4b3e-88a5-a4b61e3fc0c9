package internal

import (
	"context"
	"fmt"
	"sort"
	"time"

	"cloud.google.com/go/civil"
	gS "cloud.google.com/go/spanner"
	entity "github.com/BeReal-App/backend-go/domains/entity"
	"github.com/BeReal-App/backend-go/domains/entity/moment-grpc-api/models"
	"github.com/BeReal-App/backend-go/domains/entity/user-grpc-api/internal/metrics"
	pbCommon "github.com/BeReal-App/backend-go/proto/common/core/v1"
	pbUser "github.com/BeReal-App/backend-go/proto/entity/user/v1"
	pbPost "github.com/BeReal-App/backend-go/proto/private/post/v2"
	"github.com/BeReal-App/backend-go/shared/model"
	"github.com/BeReal-App/backend-go/shared/scope"
	"github.com/BeReal-App/backend-go/shared/util"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (s *Server) GetStreak(ctx context.Context, req *pbUser.GetStreakRequest) (*pbUser.GetStreakResponse, error) {
	row, err := s.spannerClient.Single().ReadRowWithOptions(
		ctx,
		entity.UserStreakTableName,
		gS.Key{req.UserId},
		entity.UserStreakDefaultMapper.Columns(),
		&gS.ReadOptions{RequestTag: "GetStreak"},
	)
	if err != nil {
		if gS.ErrCode(err) == codes.NotFound {
			return nil, status.Errorf(codes.NotFound, "UserStreak not found [userId=%s]", req.UserId)
		}
		return nil, fmt.Errorf("Single().ReadRowWithOptions() [userId=%s]: %w", req.UserId, err)
	}

	var streak entity.UserStreak
	if err = row.Columns(entity.UserStreakDefaultMapper.Addrs(&streak)...); err != nil {
		return nil, fmt.Errorf("row.Columns() [userId=%s]: %w", req.UserId, err)
	}

	// Get current and previous moments with error handling
	moments := s.momentsClient.GetCurrentAndPreviousByRegion()
	if moments == nil {
		// Log warning but don't fail the request - fallback to not resetting the streak
		l := scope.GetLoggerForCallsite(ctx, "GetStreak")
		l.Warn().Msg("failed to get current and previous moments, using empty slice")
		moments = []models.Moment{}
	}

	return &pbUser.GetStreakResponse{
		Streak: correctStreak(ctx, streak, moments),
	}, nil
}

func (s *Server) GetStreaks(ctx context.Context, req *pbUser.GetStreaksRequest) (*pbUser.GetStreaksResponse, error) {
	userIDs := req.GetUserIds()
	streaks := make(map[string]*pbUser.Streak)

	// Get current and previous moments with error handling
	moments := s.momentsClient.GetCurrentAndPreviousByRegion()
	if moments == nil {
		// Log warning but don't fail the request - fallback to not resetting the streak
		l := scope.GetLoggerForCallsite(ctx, "GetStreaks")
		l.Warn().Msg("failed to get current and previous moments, using empty slice")
		moments = []models.Moment{}
	}

	// Convert userIDs to Spanner KeySet
	keys := make([]gS.Key, len(userIDs))
	for i, id := range userIDs {
		keys[i] = gS.Key{id}
	}
	keySet := gS.KeySetFromKeys(keys...)

	// Use ReadWithOptions for efficient key-based reads
	iter := s.spannerClient.Single().ReadWithOptions(
		ctx,
		entity.UserStreakTableName,
		keySet,
		entity.UserStreakDefaultMapper.Columns(),
		&gS.ReadOptions{RequestTag: "GetStreaks"},
	)

	err := iter.Do(func(row *gS.Row) error {
		var streak entity.UserStreak
		if err := row.Columns(entity.UserStreakDefaultMapper.Addrs(&streak)...); err != nil {
			return fmt.Errorf("row.Columns() [userId=%s]: %w", streak.UserID, err)
		}
		streaks[streak.UserID] = correctStreak(ctx, streak, moments)
		return nil
	})
	if err != nil {
		return nil, err
	}

	return &pbUser.GetStreaksResponse{
		Streaks: streaks,
	}, nil
}

func (s *Server) UpdateStreak(ctx context.Context, req *pbUser.UpdateStreakRequest) (*pbUser.UpdateStreakResponse, error) {
	e := entity.UserStreak{
		UserID:              req.UserId,
		Length:              int64(req.Length),
		LastPostCalendarDay: req.LastPostCalendarDay.SpannerNullDate(),
		LastPostMomentID:    gS.NullString{StringVal: req.LastPostMomentId, Valid: req.LastPostMomentId != ""},
		UpdatedAt:           gS.CommitTimestamp,
		UpdatedBy:           util.UpdationSourceFromCtx(ctx),
	}

	resp, err := s.spannerClient.ReadWriteTransactionWithOptions(
		ctx,
		func(_ context.Context, tx *gS.ReadWriteTransaction) error {
			return tx.BufferWrite([]*gS.Mutation{gS.InsertOrUpdate(
				entity.UserStreakTableName,
				entity.UserStreakDefaultMapper.Columns(),
				entity.UserStreakDefaultMapper.Values(&e),
			)})
		},
		gS.TransactionOptions{
			CommitOptions:  gS.CommitOptions{MaxCommitDelay: s.streakCommitDelay, ReturnCommitStats: false},
			TransactionTag: "UpdateStreak",
		},
	)
	if err != nil {
		return nil, fmt.Errorf("on s.spannerClient.ReadWriteTransactionWithOptions() [userId=%s]: %w", req.UserId, err)
	}

	return &pbUser.UpdateStreakResponse{
		UpdatedAt: timestamppb.New(resp.CommitTs),
	}, nil
}

func correctStreak(ctx context.Context, streak entity.UserStreak, moments []models.Moment) *pbUser.Streak {
	// Create a copy to avoid modifying the original parameter
	streakCopy := streak

	if streakCopy.Length > 0 && streakCopy.LastPostMomentID.Valid {
		isOutdated := isStreakOutdated(ctx, streakCopy.LastPostMomentID.StringVal, moments)

		// Track metric for outdated/not outdated streaks
		if isOutdated {
			metrics.StreakStatusCounter.WithLabelValues("true").Inc()
			streakCopy.Length = 0
			streakCopy.LastPostCalendarDay = gS.NullDate{}
		} else {
			metrics.StreakStatusCounter.WithLabelValues("false").Inc()
		}
	}

	return streakCopy.Proto()
}

// isStreakOutdated checks if the streak is outdated by verifying if the LastPostMomentID does NOT exist
// in the current and previous moments. If the last post was made in an older moment (not in current or previous),
// then the streak should be reset to 0.
func isStreakOutdated(ctx context.Context, lastPostMomentID string, moments []models.Moment) bool {
	l := scope.GetLoggerForCallsite(ctx, "isStreakOutdated")

	if moments == nil {
		l.Warn().Msg("moments slice is nil, considering streak not outdated")
		return false
	}

	// Check if the LastPostMomentID exists in current and previous moments
	for _, moment := range moments {
		if moment.ID == lastPostMomentID {
			return false
		}
	}

	return true
}

// CalculateStreakRecovery calculates what the user's streak would be if missing days were filled
func (s *Server) CalculateStreakRecovery(ctx context.Context, req *pbUser.CalculateStreakRecoveryRequest) (*pbUser.CalculateStreakRecoveryResponse, error) {
	l := scope.GetLoggerForCallsite(ctx, "CalculateStreakRecovery")

	// Determine the number of days to look back, defaulting to 30 if not set or invalid
	numberOfDays := int(req.NumberOfDays)
	if numberOfDays <= 0 {
		numberOfDays = 30
	}

	// Get user's basic info to determine region
	userBasicInfo, err := s.getUserBasicInfo(ctx, req.UserId)
	if err != nil {
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: fmt.Sprintf("Failed to get user info: %v", err),
			},
		}, nil
	}

	// Get current streak
	currentStreak, err := s.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: req.UserId})
	if err != nil {
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: fmt.Sprintf("Failed to get current streak: %v", err),
			},
		}, nil
	}

	// Parse user's region
	userRegion := model.NewRegion(userBasicInfo.Region)
	if userRegion == model.RegionUnknown {
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: "Invalid user region",
			},
		}, nil
	}

	// Get all moments for the user's region
	allMomentsByRegion := s.momentsClient.GetAllByRegion()
	userRegionMoments, exists := allMomentsByRegion[userRegion]
	if !exists || userRegionMoments.Len() == 0 {
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: "No moments found for user region",
			},
		}, nil
	}

	// Sort moments by FiredAt desc (most recent first)
	sortedMoments := make([]models.Moment, 0, userRegionMoments.Len())
	userRegionMomentsList := userRegionMoments.AsMutableSlice()
	for _, moment := range userRegionMomentsList {
		// Only include fired moments
		if moment.FiredAt != nil {
			sortedMoments = append(sortedMoments, moment)
		}
	}

	sort.Slice(sortedMoments, func(i, j int) bool {
		return sortedMoments[i].FiredAt.After(*sortedMoments[j].FiredAt)
	})

	// Get all user's posts (no time limit for estimated streak calculation)
	// We need all posts because the estimated streak calculation must go back
	// beyond the lookback period to find where the streak would actually break
	userPosts, err := s.getAllUserPosts(ctx, req.UserId)
	if err != nil {
		l.Warn().Err(err).Msg("Failed to get user posts, continuing with empty slice")
		userPosts = []UserPost{}
	}

	// Create a map of moment IDs to check if user posted
	postsByMomentID := make(map[string]bool)
	for _, post := range userPosts {
		if post.MomentID.Valid {
			postsByMomentID[post.MomentID.StringVal] = true
		}
	}

	// Find gaps in the last N days (recovery is only allowed for recent gaps)
	now := time.Now()
	lookbackAgo := now.AddDate(0, 0, -numberOfDays)
	var gapsToFill []*pbUser.StreakGap

	// Track which dates have already been added
	dateAdded := make(map[string]bool)

	for _, moment := range sortedMoments {
		// Only consider moments from the last N days for gap filling
		if moment.FiredAt.Before(lookbackAgo) {
			continue
		}

		// Generate a date key in YYYY-MM-DD format
		dateKey := moment.FiredAt.Format("2006-01-02")

		// Skip if we've already added a gap for this date
		if dateAdded[dateKey] {
			continue
		}

		// Check if user posted for this moment
		if !postsByMomentID[moment.ID] {
			gapsToFill = append(gapsToFill, &pbUser.StreakGap{
				Date: &pbCommon.Date{
					Year:  int32(moment.FiredAt.Year()),
					Month: int32(moment.FiredAt.Month()),
					Day:   int32(moment.FiredAt.Day()),
				},
				MomentId: moment.ID,
			})
			dateAdded[dateKey] = true
		}
	}

	// Calculate estimated streak after filling gaps (uses all posts to find true break point)
	estimatedStreak := s.calculateEstimatedStreak(ctx, req.UserId, sortedMoments, postsByMomentID, gapsToFill)

	return &pbUser.CalculateStreakRecoveryResponse{
		Calculation: &pbUser.StreakRecoveryCalculation{
			CurrentStreak:   currentStreak.Streak.Length,
			EstimatedStreak: estimatedStreak,
			GapsToFill:      gapsToFill,
			IsEligible:      len(gapsToFill) > 0 && len(gapsToFill) <= 10,
		},
	}, nil
}

// ApplyStreakRecovery applies streak recovery by inserting compensation records and recomputing the streak
func (s *Server) ApplyStreakRecovery(ctx context.Context, req *pbUser.ApplyStreakRecoveryRequest) (*pbUser.ApplyStreakRecoveryResponse, error) {
	l := scope.GetLoggerForCallsite(ctx, "ApplyStreakRecovery")

	l.Info().
		Str("user_id", req.UserId).
		Int("gaps_count", len(req.GapsToFill)).
		Msg("ApplyStreakRecovery: Starting streak recovery application")

	// Validate input
	if req.UserId == "" {
		return nil, status.Errorf(codes.InvalidArgument, "invalid user ID: empty")
	}

	if len(req.GapsToFill) == 0 {
		return nil, status.Errorf(codes.InvalidArgument, "no gaps provided for recovery")
	}

	if len(req.GapsToFill) > 10 {
		return nil, status.Errorf(codes.InvalidArgument, "too many gaps for recovery: %d (max 10)", len(req.GapsToFill))
	}

	// Get user's basic info to verify they exist
	userBasicInfo, err := s.getUserBasicInfo(ctx, req.UserId)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info for userID %s: %w", req.UserId, err)
	}

	// Insert StreakCompensation records
	err = s.insertStreakCompensationRecords(ctx, req.UserId, req.GapsToFill)
	if err != nil {
		return nil, fmt.Errorf("failed to insert streak compensation records: %w", err)
	}

	// Get the updated streak after compensation
	newStreakResponse, err := s.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: req.UserId})
	if err != nil {
		return nil, fmt.Errorf("failed to get updated streak after compensation: %w", err)
	}

	l.Info().
		Str("user_id", req.UserId).
		Uint64("new_streak_length", newStreakResponse.Streak.Length).
		Str("region", userBasicInfo.Region).
		Msg("ApplyStreakRecovery: Completed successfully")

	return &pbUser.ApplyStreakRecoveryResponse{
		NewStreak: newStreakResponse.Streak,
	}, nil
}

// insertStreakCompensationRecords inserts multiple StreakCompensation records in a single transaction
func (s *Server) insertStreakCompensationRecords(ctx context.Context, userID string, gaps []*pbUser.StreakGap) error {
	l := scope.GetLoggerForCallsite(ctx, "insertStreakCompensationRecords")

	// Prepare mutations for batch insert
	var mutations []*gS.Mutation
	for _, gap := range gaps {
		// Convert pbCommon.Date to civil.Date for the Date column
		spannerDate := civil.Date{
			Year:  int(gap.Date.Year),
			Month: time.Month(gap.Date.Month),
			Day:   int(gap.Date.Day),
		}

		mutation := gS.InsertOrUpdate(
			"StreakCompensation",
			[]string{"UserId", "Date", "MomentID"},
			[]interface{}{userID, spannerDate, gap.MomentId},
		)
		mutations = append(mutations, mutation)

		l.Debug().
			Str("user_id", userID).
			Str("date", spannerDate.String()).
			Str("moment_id", gap.MomentId).
			Msg("Prepared StreakCompensation mutation")
	}

	// Execute batch insert in a transaction
	_, err := s.spannerClient.ReadWriteTransactionWithOptions(
		ctx,
		func(_ context.Context, tx *gS.ReadWriteTransaction) error {
			return tx.BufferWrite(mutations)
		},
		gS.TransactionOptions{
			TransactionTag: "ApplyStreakRecovery",
		},
	)
	if err != nil {
		return fmt.Errorf("failed to insert StreakCompensation records: %w", err)
	}

	l.Info().
		Str("user_id", userID).
		Int("records_inserted", len(gaps)).
		Msg("insertStreakCompensationRecords: Successfully inserted compensation records")

	return nil
}

// UserPost represents a simplified post structure for streak calculations
type UserPost struct {
	PostID    string
	UserID    string
	MomentID  gS.NullString
	CreatedAt time.Time
}

// getAllUserPosts retrieves all user posts (no time limit) via post service
func (s *Server) getAllUserPosts(ctx context.Context, userID string) ([]UserPost, error) {
	// Call the post service to get user posts
	resp, err := s.postClient.GetPostsOfUser(ctx, &pbPost.GetPostsOfUserRequest{
		UserId: userID,
		Order: &pbPost.OrderBy{
			Field:     pbPost.OrderBy_FIELD_CREATED_AT,
			Direction: pbPost.OrderBy_DIRECTION_DESC,
		},
		IncludeDeleted: func() *bool { b := false; return &b }(), // Exclude deleted posts for streak calculation
		SearchAll:      func() *bool { b := true; return &b }(),  // Search full table for all posts
		// No limit - we need all posts for accurate streak calculation
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get user posts from post service: %w", err)
	}

	// Convert proto posts to UserPost structs
	var posts []UserPost
	for _, protoPost := range resp.Posts {
		post := UserPost{
			PostID:    protoPost.Id,
			UserID:    protoPost.UserId,
			CreatedAt: protoPost.CreatedAt.AsTime(),
		}

		// Set MomentID if available
		if protoPost.MomentId != "" {
			post.MomentID = gS.NullString{StringVal: protoPost.MomentId, Valid: true}
		} else {
			post.MomentID = gS.NullString{Valid: false}
		}

		posts = append(posts, post)
	}

	return posts, nil
}

// calculateEstimatedStreak calculates what the streak would be if gaps were filled
// This function uses all user posts (not time-limited) to determine where the streak would break
func (s *Server) calculateEstimatedStreak(_ context.Context, _ string, sortedMoments []models.Moment, postsByMomentID map[string]bool, gapsToFill []*pbUser.StreakGap) uint64 {
	// Create a map of gaps to fill for easier lookup
	gapMomentIDs := make(map[string]bool)
	for _, gap := range gapsToFill {
		gapMomentIDs[gap.MomentId] = true
	}

	// Calculate streak from most recent moment backwards
	// Since we have all user posts, we can accurately determine where the streak would break
	consecutiveDays := 0

	for _, moment := range sortedMoments {
		// Check if user posted or if this is a gap we plan to fill
		hasPost := postsByMomentID[moment.ID] || gapMomentIDs[moment.ID]

		if hasPost {
			consecutiveDays++
		} else {
			// Break in the streak - stop counting
			break
		}
	}

	return uint64(consecutiveDays)
}

// sortGapsByDate sorts streak gaps by date (oldest first)
func sortGapsByDate(gaps []*pbUser.StreakGap) {
	sort.Slice(gaps, func(i, j int) bool {
		dateI := gaps[i].Date
		dateJ := gaps[j].Date

		if dateI.Year != dateJ.Year {
			return dateI.Year < dateJ.Year
		}
		if dateI.Month != dateJ.Month {
			return dateI.Month < dateJ.Month
		}
		return dateI.Day < dateJ.Day
	})
}

// calculateGapsFromPosts calculates streak gaps from user posts within a lookback period
func (s *Server) calculateGapsFromPosts(_ context.Context, _ string, userPosts map[string]bool, moments []models.Moment, lookbackDays int64, _ civil.Date, _ string) ([]*pbUser.StreakGap, error) {
	var gaps []*pbUser.StreakGap
	now := time.Now()
	lookbackTime := now.AddDate(0, 0, -int(lookbackDays))

	for _, moment := range moments {
		// Skip if moment is outside lookback period
		if moment.FiredAt != nil && moment.FiredAt.Before(lookbackTime) {
			continue
		}

		// Convert moment date to string format for comparison
		var momentDateStr string
		if moment.FiredAt != nil {
			momentDateStr = moment.FiredAt.Format("2006-01-02")
		} else {
			// Skip unfired moments
			continue
		}

		// Check if user posted on this day
		if !userPosts[momentDateStr] {
			gaps = append(gaps, &pbUser.StreakGap{
				Date: &pbCommon.Date{
					Year:  int32(moment.FiredAt.Year()),
					Month: int32(moment.FiredAt.Month()),
					Day:   int32(moment.FiredAt.Day()),
				},
				MomentId: moment.ID,
			})
		}
	}

	// Sort gaps by date (oldest first)
	sortGapsByDate(gaps)

	return gaps, nil
}
