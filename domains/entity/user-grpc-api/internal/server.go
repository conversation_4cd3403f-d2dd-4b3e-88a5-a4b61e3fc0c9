package internal

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/BeReal-App/backend-go/domains/entity/place"

	gPS "cloud.google.com/go/pubsub"
	gS "cloud.google.com/go/spanner"
	"cloud.google.com/go/spanner/apiv1/spannerpb"
	pbPostv2 "github.com/BeReal-App/backend-go/proto/private/post/v2"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/BeReal-App/backend-go/domains/entity"
	"github.com/BeReal-App/backend-go/domains/entity/moment"
	user_cache "github.com/BeReal-App/backend-go/domains/entity/user-cache"
	"github.com/BeReal-App/backend-go/domains/entity/user-grpc-api/internal/metrics"
	pbUser "github.com/BeReal-App/backend-go/proto/entity/user/v1"
	"github.com/samber/lo"

	safety "github.com/BeReal-App/backend-go/domains/safety/queue-publisher"
	pbCommon "github.com/BeReal-App/backend-go/proto/common/core/v1"
	pbSafetyCore "github.com/BeReal-App/backend-go/proto/safety/core/v1"
	pbSafetyQueue "github.com/BeReal-App/backend-go/proto/safety/queue/v1"

	"github.com/BeReal-App/backend-go/shared/bpubsub"
	"github.com/BeReal-App/backend-go/shared/constant"
	"github.com/BeReal-App/backend-go/shared/data"
	bgrpc "github.com/BeReal-App/backend-go/shared/grpc"
	"github.com/BeReal-App/backend-go/shared/phonenumbers"
	"github.com/BeReal-App/backend-go/shared/scope"
	"github.com/BeReal-App/backend-go/shared/spanner"
	"github.com/BeReal-App/backend-go/shared/util"
)

var (
	ErrFieldIsUpdatedAndCleared = errors.New("field is updated and cleared at the same time")
	ErrUsernameAlreadyExists    = bgrpc.NewErrorCounter(
		status.Error(codes.AlreadyExists, "username already exists"),
		metrics.UsernameAlreadyExistsCounter,
	)
	DefaultBucket = "storage.bere.al"

	_ pbUser.UserServiceServer = (*Server)(nil)
)

func NewServer(
	pubsubtopic *gPS.Topic,
	spannerClient *gS.Client,
	basicInfoCache user_cache.BasicInfo,
	realmojisCache user_cache.Realmojis,
	sqp *safety.Publisher,
	streakCommitDelay *time.Duration,
	momentsClient *moment.Client,
	placesService place.Service,
	postClient pbPostv2.PostServiceClient,
) (*Server, error) {
	return &Server{
		spannerClient:        spannerClient,
		basicInfoCache:       basicInfoCache,
		realmojisCache:       realmojisCache,
		safetyQueuePublisher: sqp,
		pubsubtopic:          pubsubtopic,
		streakCommitDelay:    streakCommitDelay,
		momentsClient:        momentsClient,
		placesStorageService: placesService,
		postClient:           postClient,
	}, nil
}

type Server struct {
	spannerClient  *gS.Client
	basicInfoCache user_cache.BasicInfo
	realmojisCache user_cache.Realmojis

	oaCache *pbUser.GetOAUsersResponse
	oaMu    sync.RWMutex

	safetyQueuePublisher *safety.Publisher

	pubsubtopic *gPS.Topic

	streakCommitDelay *time.Duration

	momentsClient *moment.Client

	placesStorageService place.Service

	postClient pbPostv2.PostServiceClient
}

func (s *Server) CreateUser(context.Context, *pbUser.CreateUserRequest) (*pbUser.CreateUserResponse, error) {
	// TODO: implement this method quickly
	return nil, status.Errorf(codes.Unimplemented, "method CreateUser not implemented")
}

func (s *Server) UpdateUser(ctx context.Context, req *pbUser.UpdateUserRequest) (*pbUser.UpdateUserResponse, error) {
	l := scope.GetLogger(ctx)
	cols := []string{}
	values := []interface{}{}
	clearedFields := data.NewSet[string]()
	var mutations []*gS.Mutation

	if req.Clear != nil {
		if req.Clear.Birthdate {
			clearedFields.Add("Birthdate")
			cols = append(cols, "Birthdate")
			values = append(values, nil)
		}
		if req.Clear.ProfilePicture {
			clearedFields.AddAll("ProfilePicture")
			cols = append(cols, "ProfilePicturePath", "ProfilePictureBucket", "ProfilePictureWidth", "ProfilePictureHeight")
			values = append(values, nil, nil, nil, nil)
		}
		if req.Clear.Links {
			clearedFields.Add("Links")
			cols = append(cols, "LinksBytes")
			values = append(values, nil)
		}
	}

	if req.Update != nil {
		if req.Update.Username != nil {
			cols = append(cols, "Username")
			values = append(values, req.Update.Username)
		}
		if req.Update.PhoneNumber != nil {
			cc, err := phonenumbers.GetAlpha2CountryCode(*req.Update.PhoneNumber)
			if err == phonenumbers.ErrUnknownCountryCode {
				cc = constant.UnknownCountryCode
			} else if err != nil {
				return nil, err
			}
			cols = append(cols, "PhoneNumber", "CountryCode")
			values = append(values, req.Update.PhoneNumber, cc)
		}
		if req.Update.Birthdate != nil {
			if clearedFields.Contains("Birthdate") {
				l.Error().
					Err(ErrFieldIsUpdatedAndCleared).
					Str("field", "Birthdate").
					Msgf("birthdate: %s", ErrFieldIsUpdatedAndCleared.Error())
			} else {
				cols = append(cols, "Birthdate")
				values = append(values, req.Update.Birthdate.DateString())
			}
		}
		if req.Update.Region != nil {
			cols = append(cols, "Region")
			values = append(values, req.Update.Region)
		}
		if req.Update.Fullname != nil {
			cols = append(cols, "Fullname")
			values = append(values, req.Update.Fullname)
		}
		if req.Update.Language != nil {
			cols = append(cols, "Language")
			values = append(values, req.Update.Language)
		}
		if req.Update.ClientVersion != nil {
			cols = append(cols, "ClientVersion")
			values = append(values, req.Update.ClientVersion)
		}

		if req.Update.Device != nil {
			cols = append(cols, "Device")
			values = append(values, req.Update.Device)
		}
		if req.Update.DeviceId != nil {
			cols = append(cols, "DeviceID")
			values = append(values, req.Update.DeviceId)
		}
		if req.Update.Timezone != nil {
			cols = append(cols, "Timezone")
			values = append(values, req.Update.Timezone)
		}
		if req.Update.Platform != nil {
			cols = append(cols, "Platform")
			values = append(values, req.Update.Platform.Spanner())
		}
		if req.Update.Biography != nil {
			cols = append(cols, "Biography")
			values = append(values, req.Update.Biography)
		}
		if req.Update.Location != nil {
			cols = append(cols, "Location")
			values = append(values, req.Update.Location)
		}
		if req.Update.ProfilePicture != nil {
			if clearedFields.Contains("ProfilePicture") {
				l.Error().
					Err(ErrFieldIsUpdatedAndCleared).
					Str("field", "ProfilePicture").
					Msgf("profilePicture: %s", ErrFieldIsUpdatedAndCleared.Error())
			} else {
				cols = append(
					cols,
					"ProfilePicturePath",
					"ProfilePictureBucket",
					"ProfilePictureWidth",
					"ProfilePictureHeight",
				)
				values = append(
					values,
					&req.Update.ProfilePicture.Path,
					&req.Update.ProfilePicture.Bucket,
				)
				if req.Update.ProfilePicture.Width == 0 {
					values = append(values, nil)
				} else {
					values = append(values, int64(req.Update.ProfilePicture.Width))
				}
				if req.Update.ProfilePicture.Height == 0 {
					values = append(values, nil)
				} else {
					values = append(values, int64(req.Update.ProfilePicture.Height))
				}
			}
		}
		if req.Update.Bucket != nil {
			cols = append(cols, "Bucket")
			values = append(values, req.Update.Bucket)
		}
		if req.Update.Type != nil {
			cols = append(cols, "Type")
			values = append(values, req.Update.Type.Spanner())
		}
		if req.Update.Links != nil {
			if clearedFields.Contains("Links") {
				l.Error().
					Err(ErrFieldIsUpdatedAndCleared).
					Str("field", "Links").
					Msgf("links: %s", ErrFieldIsUpdatedAndCleared.Error())
			} else {
				lb, err := marshalLinks(req.Update.Links)
				if err != nil {
					return nil, err
				}
				cols = append(cols, "LinksBytes")
				values = append(values, lb)
			}
		}
		if req.Update.Visibility != nil {
			cols = append(cols, "Visibility")
			values = append(values, req.Update.Visibility.Spanner())
		}
		if req.Update.Gender != nil {
			cols = append(cols, "Gender")
			values = append(values, req.Update.Gender.Spanner())
		}
		if req.Update.AstroSign != nil {
			cols = append(cols, "AstroSign")
			values = append(values, req.Update.AstroSign.Spanner())
		}
		if req.Update.Occupation != nil {
			cols = append(cols, "Occupation")
			values = append(values, req.Update.Occupation)
		}
		if req.Update.School != nil {
			cols = append(cols, "School")
			values = append(values, req.Update.School)
		}
	}

	if len(cols) > 0 {
		mutations = append(mutations, gS.Update(
			entity.UserTableName,
			append(
				cols,
				"userId",
				"updatedAt",
				"updatedBy",
			),
			append(
				values,
				req.UserId,
				"spanner.commit_timestamp()",
				util.UpdationSourceFromCtx(ctx),
			),
		))
	}

	if len(mutations) <= 0 {
		return &pbUser.UpdateUserResponse{
			UpdatedAt: timestamppb.New(time.Now()),
		}, nil
	}

	commitTS, err := s.spannerClient.Apply(
		ctx,
		mutations,
		gS.TransactionTag("UpdateUser"),
	)
	if err != nil {
		if e, ok := status.FromError(err); ok {
			if e.Code() == codes.AlreadyExists && strings.Contains(err.Error(), "Users_by_username_uniq") {
				return nil, ErrUsernameAlreadyExists
			}
		}
		return nil, err
	}

	if s.safetyQueuePublisher != nil {
		if req.Update != nil && req.Update.ProfilePicture != nil {
			s.safetyQueuePublisher.Publish(
				&pbSafetyQueue.Input{
					User: &pbSafetyQueue.User{
						Id: req.UserId,
					},
					Entity: &pbSafetyQueue.Entity{
						Id:        req.UserId,
						Type:      pbSafetyCore.EntityType_ENTITY_TYPE_USER,
						CreatedAt: timestamppb.New(commitTS),
					},
					Event: &pbSafetyQueue.Input_ProfilePhotoCreated{
						ProfilePhotoCreated: &pbSafetyQueue.ProfilePhotoCreatedEvent{
							Media: &pbSafetyQueue.ClassifiableMedia{
								Reference: &pbCommon.MediaReference{
									// This ID allows us to upsert new profile pictures and easily override
									// the existing one without needing to manually delete the old one.
									Id:  req.UserId + "-profile-photo",
									Url: req.Update.ProfilePicture.Bucket + ":" + req.Update.ProfilePicture.Path,
								},
							},
						},
					},
				})
		}
	}

	if s.basicInfoCache.IsEnabled() {
		if err := s.basicInfoCache.Evict(ctx, req.UserId); err != nil {
			l.Err(err).Msgf("error while evicting user from cache")
		}
	}

	return &pbUser.UpdateUserResponse{
		UpdatedAt: timestamppb.New(commitTS),
	}, nil
}

func (s *Server) UpdateUserActivity(ctx context.Context, req *pbUser.UpdateUserActivityRequest) (*pbUser.UpdateUserActivityResponse, error) {
	mutation := gS.InsertOrUpdateMap(entity.UserActivityTableName, map[string]interface{}{
		"UserId":       req.UserId,
		"LastActiveAt": req.LastActiveAt.AsTime(),
	})

	_, err := s.spannerClient.Apply(ctx, []*gS.Mutation{mutation})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to record user activity: %v", err)
	}

	return &pbUser.UpdateUserActivityResponse{LastActiveAt: req.LastActiveAt}, nil
}

func (s *Server) GetUserActivity(ctx context.Context, req *pbUser.GetUserActivityRequest) (*pbUser.GetUserActivityResponse, error) {
	row, err := s.spannerClient.Single().ReadRow(ctx, "UserActivity", gS.Key{req.UserId}, []string{"LastActiveAt"})
	if err != nil {
		if errors.Is(err, gS.ErrRowNotFound) {
			return nil, status.Errorf(codes.NotFound, "user not found: %s", req.UserId)
		}
		return nil, status.Errorf(codes.Internal, "failed to get user activity: %v", err)
	}

	var lastActiveAt gS.NullTime
	if err := row.ColumnByName("LastActiveAt", &lastActiveAt); err != nil {
		return nil, status.Errorf(codes.Internal, "failed to decode last time active: %v", err)
	}

	var lastActiveAtProto *timestamppb.Timestamp
	if lastActiveAt.Valid {
		lastActiveAtProto = timestamppb.New(lastActiveAt.Time)
	}

	return &pbUser.GetUserActivityResponse{
		LastActiveAt: lastActiveAtProto,
	}, nil
}

func (s *Server) DeleteUser(ctx context.Context, req *pbUser.DeleteUserRequest) (*pbUser.DeleteUserResponse, error) {
	l := scope.GetLoggerForCallsite(ctx, "DeleteUser")

	// getting profile to populate pubsub event BEFORE deleting informations
	// so we have data needed
	profile, _, err := s.getUserProfile(ctx, &pbUser.GetUserRequest{
		UserId: req.UserId,
		Structs: &pbUser.GetUserRequest_Structs{
			Profile: true,
		},
	})
	if err != nil {
		st, ok := status.FromError(err)
		if !ok || st.Code() != codes.NotFound {
			l.Warn().Err(err).Msgf("on s.getUserProfile() while deleting user: %s", err.Error())
		}
	}

	// deleting informations
	commitTS, err := s.spannerClient.Apply(
		ctx,
		[]*gS.Mutation{
			gS.Delete(entity.UserTableName, gS.Key{req.UserId}),
			// the deletion part could be removed after migration is done
			// this is there to make we do not revive users while migrating data from CloudSQL to spanner
			gS.InsertOrUpdate(
				entity.UserDeletionTableName,
				[]string{"UserId", "CreatedAt", "CreatedBy"},
				[]interface{}{req.UserId, "spanner.commit_timestamp()", util.UpdationSourceFromCtx(ctx)},
			),
		},
		gS.TransactionTag("DeleteUser"),
		gS.Priority(spannerpb.RequestOptions_PRIORITY_LOW),
	)
	if err != nil {
		return nil, err
	}

	if s.basicInfoCache.IsEnabled() {
		if err := s.basicInfoCache.Evict(ctx, req.UserId); err != nil {
			l.Err(err).Msgf("error while evicting user from cache")
		}
	}

	// send the pubsub event for async cleanup purposes (mostly GDPR)
	data := `{"id":"` + req.UserId + `"}`
	if profile != nil && profile.PhoneNumber != "" {
		data = `{"id":"` + req.UserId + `","phoneNumber":"` + profile.PhoneNumber + `"}`
	}
	_, err = s.pubsubtopic.Publish(ctx, &gPS.Message{
		Data: []byte(data),
		Attributes: map[string]string{
			"operation": bpubsub.MutationOperationDeleted.String(),
		},
	}).Get(ctx)
	if err != nil {
		l.Err(err).Msgf("error while publishing user deletion")
	}

	return &pbUser.DeleteUserResponse{
		UpdatedAt: timestamppb.New(commitTS),
	}, nil
}

func (s *Server) SearchUsers(ctx context.Context, req *pbUser.SearchUsersRequest) (*pbUser.SearchUsersResponse, error) {
	vpUsers, err := s.SearchOA(req.GetQuery(), 5)
	if err != nil {
		return nil, fmt.Errorf("on search verified users: %w", err)
	}

	vpUserIDs := lo.Map(vpUsers, func(u *entity.SearchUser, _ int) string { return u.ID })
	req.ExcludeUserIds = lo.Uniq(append(req.ExcludeUserIds, vpUserIDs...))

	users, err := searchUsers(ctx, req, s.spannerClient)
	if err != nil {
		return nil, fmt.Errorf("on search users: %w", err)
	}

	protoUsers := make([]*pbUser.SearchUser, 0, len(vpUsers)+len(users))
	for _, u := range vpUsers {
		protoUsers = append(protoUsers, u.ToProto())
	}
	for _, u := range users {
		protoUsers = append(protoUsers, u.ToProto())
	}

	return &pbUser.SearchUsersResponse{
		Users: protoUsers,
	}, nil
}

func (s *Server) FindUsersByUsername(ctx context.Context, req *pbUser.FindUsersByUsernameRequest) (*pbUser.FindUsersByUsernameResponse, error) {
	iter := s.spannerClient.Single().QueryWithOptions(
		ctx,
		gS.Statement{
			SQL: `
				SELECT ` + entity.SearchUserMapper.ColumnsString() + `
				FROM ` + entity.UserTableName + `
				WHERE Username IN UNNEST(@usernames)
			`,
			Params: map[string]interface{}{
				"usernames": req.GetUsernames(),
			},
		},
		gS.QueryOptions{
			RequestTag: "FindUsersByUsername",
		},
	)

	foundUsers := make([]*pbUser.SearchUser, 0, len(req.Usernames))

	err := iter.Do(func(row *gS.Row) error {
		var user entity.SearchUser
		if err := row.Columns(entity.SearchUserMapper.Addrs(&user)...); err != nil {
			return fmt.Errorf("failed to get column: %w", err)
		}

		foundUsers = append(foundUsers, user.ToProto())
		return nil
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get rows: %w", err)
	}

	return &pbUser.FindUsersByUsernameResponse{
		FoundUsers: foundUsers,
	}, nil
}

// TODO: for data migration it should be removed at the end of the process
func (s *Server) InsertUser(ctx context.Context, req *pbUser.InsertUserRequest) (*pbUser.InsertUserResponse, error) {
	createdBy := util.UpdationSourceFromCtx(ctx)

	// visibility per default is PUBLIC
	visibility := req.Visibility
	if req.Visibility == nil || *req.Visibility == pbUser.Visibility_VISIBILITY_UNSPECIFIED {
		visibility = pbUser.Visibility_VISIBILITY_PUBLIC.Enum()
	}

	e := &entity.User{
		ID:            req.UserId,
		Username:      req.Username,
		PhoneNumber:   req.PhoneNumber,
		Birthdate:     req.Birthdate.SpannerNullDate(),
		Region:        req.Region,
		Fullname:      spanner.FromString(req.Fullname),
		Language:      spanner.FromString(req.Language),
		ClientVersion: spanner.FromString(req.ClientVersion),
		Device:        spanner.FromString(req.Device),
		DeviceID:      spanner.FromString(req.DeviceId),
		Timezone:      spanner.FromString(req.Timezone),
		Platform:      req.Platform.Spanner(),
		CountryCode:   spanner.FromString(req.CountryCode),
		Bucket:        spanner.FromString(lo.Must(lo.Coalesce(req.Bucket, &DefaultBucket))),
		CreatedAt:     req.CreatedAt.AsTime(),
		UpdatedAt:     req.CreatedAt.AsTime(),
		Type:          req.Type.Spanner(),
		Visibility:    visibility.Spanner(),
		CreatedBy:     createdBy,
		UpdatedBy:     createdBy,
		Gender:        req.Gender.Spanner(),
		IsVerified:    req.IsVerified,
	}

	// links
	lb, err := marshalLinks(req.Links)
	if err != nil {
		return nil, err
	}
	e.LinksBytes = lb

	var mutations []*gS.Mutation
	mutations = append(mutations, gS.InsertOrUpdate(entity.UserTableName, entity.UserDefaultMapper.Columns(), entity.UserDefaultMapper.Values(e)))
	if _, err := s.spannerClient.Apply(
		ctx,
		mutations,
		gS.TransactionTag("InsertUser"),
	); err != nil {
		if e, ok := status.FromError(err); ok {
			if e.Code() == codes.AlreadyExists && strings.Contains(err.Error(), "Users_by_username_uniq") {
				return nil, ErrUsernameAlreadyExists
			}
		}
		return nil, err
	}

	return &pbUser.InsertUserResponse{
		UserId: req.UserId,
	}, nil
}

func (s *Server) UserExists(ctx context.Context, req *pbUser.UserExistsRequest) (*pbUser.UserExistsResponse, error) {
	// for now we can not use redis exists cmd shortcut
	// because an empty user can be set in cache to avoid fallbacking to database when this user does not exists
	// the faster way to check a user exists is to call getBasicUserInfo for now sadly
	// the abstraction is still good because we may find a faster solution later
	if _, err := s.getUserBasicInfo(ctx, req.UserId); err != nil {
		if status.Code(err) == codes.NotFound {
			return &pbUser.UserExistsResponse{
				Exists: false,
			}, nil
		}
		return nil, err
	}

	return &pbUser.UserExistsResponse{
		Exists: true,
	}, nil
}

func marshalLinks(links []*pbUser.ProfileLink) ([]byte, error) {
	if len(links) <= 0 {
		return nil, nil
	}
	l := pbUser.ProfileLinks{
		Links:     links,
		UpdatedAt: timestamppb.Now(),
	}
	lb, err := proto.Marshal(&l)
	if err != nil {
		return nil, fmt.Errorf("error while marshalling links: %w", err)
	}
	return lb, nil
}
